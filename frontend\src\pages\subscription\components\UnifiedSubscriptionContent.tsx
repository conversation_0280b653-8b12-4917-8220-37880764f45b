/**
 * 统一订阅管理内容组件
 * 整合订阅详情和套餐选择功能
 */

import {
  CheckOutlined,
  CrownOutlined,
  HistoryOutlined,
  ReloadOutlined,
  ShoppingCartOutlined,
  StarOutlined,
  StopOutlined,
  UpOutlined,
} from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  Button,
  Card,
  Col,
  Descriptions,
  Divider,
  Empty,
  InputNumber,
  List,
  Modal,
  message,
  Progress,
  Row,
  Space,
  Table,
  Tag,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import { SubscriptionService } from '@/services';
import type {
  CreateSubscriptionRequest,
  SubscriptionPlanResponse,
  SubscriptionResponse,
} from '@/types/api';
import { SubscriptionStatus } from '@/types/api';

const { Title, Text } = Typography;

interface UnifiedSubscriptionContentProps {
  currentSubscription: SubscriptionResponse | null;
  loading: boolean;
  onRefresh: () => void;
}

const UnifiedSubscriptionContent: React.FC<UnifiedSubscriptionContentProps> = ({
  currentSubscription,
  loading,
  onRefresh,
}) => {
  // 订阅详情相关状态
  const [subscriptionHistory, setSubscriptionHistory] = useState<
    SubscriptionResponse[]
  >([]);
  const [usageInfo, setUsageInfo] = useState<any>(null);
  const [historyModalVisible, setHistoryModalVisible] = useState(false);

  // 套餐选择相关状态
  const [plans, setPlans] = useState<SubscriptionPlanResponse[]>([]);
  const [plansLoading, setPlansLoading] = useState(true);
  const [subscribing, setSubscribing] = useState(false);
  const [selectedPlan, setSelectedPlan] =
    useState<SubscriptionPlanResponse | null>(null);
  const [subscribeModalVisible, setSubscribeModalVisible] = useState(false);
  const [duration, setDuration] = useState(1);

  useEffect(() => {
    fetchPlans();
    if (currentSubscription) {
      fetchSubscriptionHistory();
      fetchUsageInfo();
    }
  }, [currentSubscription]);

  // 获取套餐列表
  const fetchPlans = async () => {
    try {
      setPlansLoading(true);
      const plansData = await SubscriptionService.getActivePlans();
      setPlans(plansData);
    } catch (error) {
      console.error('获取套餐列表失败:', error);
      message.error('获取套餐列表失败');
    } finally {
      setPlansLoading(false);
    }
  };

  // 获取订阅历史
  const fetchSubscriptionHistory = async () => {
    try {
      const history = await SubscriptionService.getSubscriptionHistory();
      setSubscriptionHistory(history);
    } catch (error) {
      console.error('获取订阅历史失败:', error);
    }
  };

  // 获取使用情况
  const fetchUsageInfo = async () => {
    try {
      const usage = await SubscriptionService.getUsageInfo();
      setUsageInfo(usage);
    } catch (error) {
      console.error('获取使用情况失败:', error);
    }
  };

  // 处理订阅
  const handleSubscribe = async () => {
    if (!selectedPlan) return;

    try {
      setSubscribing(true);
      const request: CreateSubscriptionRequest = {
        planId: selectedPlan.id,
        duration: duration,
      };

      await SubscriptionService.createSubscription(request);
      message.success('订阅成功！');
      setSubscribeModalVisible(false);
      onRefresh();
    } catch (error) {
      console.error('订阅失败:', error);
      message.error('订阅失败，请稍后重试');
    } finally {
      setSubscribing(false);
    }
  };

  // 取消订阅
  const handleCancelSubscription = async () => {
    if (!currentSubscription) return;

    Modal.confirm({
      title: '确认取消订阅',
      content: '取消订阅后，您将失去当前套餐的所有权益。确定要取消吗？',
      okText: '确认取消',
      cancelText: '保留订阅',
      okType: 'danger',
      onOk: async () => {
        try {
          await SubscriptionService.cancelSubscription(currentSubscription.id);
          message.success('订阅已取消');
          onRefresh();
        } catch (error) {
          console.error('取消订阅失败:', error);
          message.error('取消订阅失败');
        }
      },
    });
  };

  // 获取状态标签
  const getStatusTag = (status: SubscriptionStatus) => {
    const statusConfig = {
      [SubscriptionStatus.ACTIVE]: { color: 'green', text: '有效' },
      [SubscriptionStatus.EXPIRED]: { color: 'red', text: '已过期' },
      [SubscriptionStatus.CANCELED]: { color: 'default', text: '已取消' },
      [SubscriptionStatus.PENDING]: { color: 'orange', text: '待激活' },
    };

    const config = statusConfig[status] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取套餐推荐标签
  const getPlanRecommendation = (plan: SubscriptionPlanResponse) => {
    if (plan.name === '标准版') {
      return (
        <Tag color="orange" icon={<StarOutlined />}>
          推荐
        </Tag>
      );
    }
    if (plan.name === '企业版') {
      return (
        <Tag color="gold" icon={<CrownOutlined />}>
          热门
        </Tag>
      );
    }
    return null;
  };

  // 套餐特性列表
  const getPlanFeatures = (plan: SubscriptionPlanResponse) => {
    const features = [
      `可创建 ${plan.maxSize === 999999 ? '无限' : plan.maxSize} 个团队`,
      '团队成员无限制',
      '数据安全保障',
      '7x24小时技术支持',
    ];

    if (plan.name !== '免费版') {
      features.push('优先客服支持');
    }
    if (plan.name === '企业版') {
      features.push('定制化服务');
      features.push('专属客户经理');
    }

    return features;
  };

  // 历史记录表格列定义
  const historyColumns: ColumnsType<SubscriptionResponse> = [
    {
      title: '套餐名称',
      dataIndex: 'planName',
      key: 'planName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: SubscriptionStatus) => getStatusTag(status),
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      key: 'endDate',
      render: (date: string) =>
        date ? new Date(date).toLocaleDateString() : '永久',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (price: number) => `¥${price.toFixed(2)}`,
    },
  ];

  return (
    <div>
      {/* 当前订阅状态 */}
      <Card
        title={
          <Space>
            <CrownOutlined />
            当前订阅状态
          </Space>
        }
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={onRefresh}
            loading={loading}
          >
            刷新
          </Button>
        }
        style={{ marginBottom: 24 }}
      >
        {currentSubscription ? (
          <div>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="套餐名称">
                <Space>
                  {currentSubscription.planName}
                  {getStatusTag(currentSubscription.status)}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="团队限制">
                {currentSubscription.maxSize === 999999
                  ? '无限制'
                  : `${currentSubscription.maxSize} 个`}
              </Descriptions.Item>
              <Descriptions.Item label="开始时间">
                {new Date(currentSubscription.startDate).toLocaleDateString()}
              </Descriptions.Item>
              <Descriptions.Item label="结束时间">
                {currentSubscription.endDate
                  ? new Date(currentSubscription.endDate).toLocaleDateString()
                  : '永久有效'}
              </Descriptions.Item>
              <Descriptions.Item label="月费">
                ¥{currentSubscription.price.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="剩余天数">
                {usageInfo?.remainingDays !== undefined
                  ? `${usageInfo.remainingDays} 天`
                  : '计算中...'}
              </Descriptions.Item>
            </Descriptions>

            {usageInfo && (
              <div style={{ marginTop: 16 }}>
                <Text strong>团队使用情况：</Text>
                <Progress
                  percent={usageInfo.usagePercentage}
                  format={() =>
                    `${usageInfo.currentUsage}/${usageInfo.maxUsage === 999999 ? '∞' : usageInfo.maxUsage}`
                  }
                  style={{ marginTop: 8 }}
                />
              </div>
            )}

            <div style={{ marginTop: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<UpOutlined />}
                  onClick={() => setSubscribeModalVisible(true)}
                >
                  升级套餐
                </Button>
                <Button
                  icon={<HistoryOutlined />}
                  onClick={() => setHistoryModalVisible(true)}
                >
                  查看历史
                </Button>
                {currentSubscription.status === SubscriptionStatus.ACTIVE && (
                  <Button
                    danger
                    icon={<StopOutlined />}
                    onClick={handleCancelSubscription}
                  >
                    取消订阅
                  </Button>
                )}
              </Space>
            </div>
          </div>
        ) : (
          <Empty
            description="暂无有效订阅"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button
              type="primary"
              icon={<ShoppingCartOutlined />}
              onClick={() => setSubscribeModalVisible(true)}
            >
              立即订阅
            </Button>
          </Empty>
        )}
      </Card>

      {/* 套餐选择 */}
      <Card
        title={
          <Space>
            <ShoppingCartOutlined />
            选择套餐
          </Space>
        }
        loading={plansLoading}
      >
        <Row gutter={[16, 16]}>
          {plans.map((plan) => (
            <Col xs={24} sm={12} lg={6} key={plan.id}>
              <Card
                hoverable
                className={`plan-card ${currentSubscription?.planId === plan.id ? 'current-plan' : ''}`}
                actions={[
                  <Button
                    key="subscribe"
                    type={
                      currentSubscription?.planId === plan.id
                        ? 'default'
                        : 'primary'
                    }
                    disabled={currentSubscription?.planId === plan.id}
                    onClick={() => {
                      setSelectedPlan(plan);
                      setSubscribeModalVisible(true);
                    }}
                  >
                    {currentSubscription?.planId === plan.id
                      ? '当前套餐'
                      : '选择此套餐'}
                  </Button>,
                ]}
              >
                <div style={{ textAlign: 'center' }}>
                  <Title level={4}>
                    {plan.name}
                    {getPlanRecommendation(plan)}
                  </Title>
                  <div
                    style={{
                      fontSize: 32,
                      fontWeight: 'bold',
                      color: '#1890ff',
                    }}
                  >
                    ¥{plan.price.toFixed(0)}
                    <span style={{ fontSize: 14, color: '#666' }}>/月</span>
                  </div>
                  <Text type="secondary">{plan.description}</Text>
                </div>

                <Divider />

                <List
                  size="small"
                  dataSource={getPlanFeatures(plan)}
                  renderItem={(feature) => (
                    <List.Item>
                      <Space>
                        <CheckOutlined style={{ color: '#52c41a' }} />
                        {feature}
                      </Space>
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 订阅确认弹窗 */}
      <Modal
        title="确认订阅"
        open={subscribeModalVisible}
        onOk={handleSubscribe}
        onCancel={() => setSubscribeModalVisible(false)}
        confirmLoading={subscribing}
        okText="确认订阅"
        cancelText="取消"
      >
        {selectedPlan && (
          <div>
            <Alert
              message={`您选择了 ${selectedPlan.name}`}
              description={selectedPlan.description}
              type="info"
              style={{ marginBottom: 16 }}
            />

            <div style={{ marginBottom: 16 }}>
              <Text strong>订阅时长：</Text>
              <InputNumber
                min={1}
                max={12}
                value={duration}
                onChange={(value) => setDuration(value || 1)}
                addonAfter="个月"
                style={{ marginLeft: 8 }}
              />
            </div>

            <div>
              <Text strong>总费用：</Text>
              <Text style={{ fontSize: 18, color: '#1890ff', marginLeft: 8 }}>
                ¥{(selectedPlan.price * duration).toFixed(2)}
              </Text>
            </div>
          </div>
        )}
      </Modal>

      {/* 订阅历史弹窗 */}
      <Modal
        title="订阅历史"
        open={historyModalVisible}
        onCancel={() => setHistoryModalVisible(false)}
        footer={null}
        width={800}
      >
        <Table
          columns={historyColumns}
          dataSource={subscriptionHistory}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Modal>
    </div>
  );
};

export default UnifiedSubscriptionContent;
