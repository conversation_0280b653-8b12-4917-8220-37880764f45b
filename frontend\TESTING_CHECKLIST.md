# 前端功能测试清单

## ✅ 验证码登录系统测试

### 基础功能测试
- [ ] **登录页面加载**
  - 访问 `/user/login`
  - 确认显示邮箱输入框和验证码输入框
  - 确认显示"发送验证码"按钮

- [ ] **发送验证码功能**
  - 输入有效邮箱，点击"发送验证码"
  - 确认按钮变为加载状态
  - 确认成功后显示倒计时（60秒）
  - 确认开发环境下显示调试信息

- [ ] **验证码登录**
  - 从后端日志获取验证码
  - 输入正确的6位验证码
  - 点击"登录"按钮
  - 确认登录成功并跳转

### 错误处理测试
- [ ] **邮箱验证**
  - 输入无效邮箱格式
  - 确认显示错误提示
  - 空邮箱时点击发送验证码，确认提示

- [ ] **验证码验证**
  - 输入错误验证码，确认提示"验证码错误"
  - 输入过期验证码，确认提示"验证码已过期"
  - 输入非6位数字，确认格式验证

- [ ] **重发限制**
  - 发送验证码后立即再次点击
  - 确认按钮禁用并显示倒计时
  - 等待60秒后确认可以重新发送

### 开发环境调试
- [ ] **调试面板**
  - 登录页面确认显示调试面板
  - 面板显示验证码获取指引
  - 5秒后自动隐藏

- [ ] **控制台输出**
  - 发送验证码后确认控制台有调试信息
  - 确认 `window.devHelper` 对象存在
  - 测试 `quickFillVerificationCode` 函数

## ✅ 邀请链接功能测试

### 发送邀请测试
- [ ] **团队管理页面**
  - 登录后访问团队管理页面
  - 点击"邀请成员"按钮
  - 输入邮箱列表和邀请消息

- [ ] **邀请发送**
  - 提交邀请表单
  - 确认显示发送成功消息
  - 确认显示发送统计（成功/失败数量）

- [ ] **邀请链接显示**
  - 在邀请列表中确认显示邀请链接列
  - 点击"复制链接"确认复制成功
  - 点击"预览"确认在新窗口打开

### 邀请链接页面测试
- [ ] **页面访问**
  - 访问有效的邀请链接 `/invite/:token`
  - 确认页面正常加载
  - 确认显示选择加入方式的界面

- [ ] **现有用户加入**
  - 选择"我已有账号，直接加入"
  - 点击"加入团队"按钮
  - 确认显示加入成功结果

- [ ] **新用户注册加入**
  - 选择"我是新用户，注册后加入"
  - 填写注册表单（姓名、邮箱、密码）
  - 提交表单确认注册成功

### 错误处理测试
- [ ] **无效链接**
  - 访问无效的token
  - 确认显示"邀请链接无效"错误

- [ ] **表单验证**
  - 新用户注册时留空必填字段
  - 确认显示相应的验证错误
  - 输入无效邮箱格式确认验证

- [ ] **网络错误**
  - 模拟网络错误
  - 确认显示友好的错误提示

## ✅ 团队邀请管理测试

### 邀请列表功能
- [ ] **列表显示**
  - 确认邀请列表正确显示
  - 确认显示邀请状态、时间等信息
  - 确认显示邀请链接操作按钮

- [ ] **链接操作**
  - 测试复制邀请链接功能
  - 测试预览邀请链接功能
  - 确认链接格式正确

- [ ] **邀请管理**
  - 测试取消邀请功能
  - 确认取消后链接失效
  - 测试邀请状态更新

## ✅ 兼容性测试

### 现有功能
- [ ] **注册功能**
  - 确认注册页面仍然可用
  - 确认密码注册流程正常
  - 确认注册后可以正常登录

- [ ] **团队功能**
  - 确认团队选择页面正常
  - 确认团队切换功能正常
  - 确认团队管理功能正常

- [ ] **其他页面**
  - 确认其他页面不受影响
  - 确认导航和路由正常
  - 确认权限控制正常

## 🔧 技术测试

### API 接口测试
- [ ] **验证码接口**
  ```bash
  # 测试发送验证码
  curl -X POST http://localhost:8080/api/v1/auth/send-code \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","type":"login"}'
  
  # 测试验证码登录
  curl -X POST http://localhost:8080/api/v1/auth/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","code":"123456"}'
  ```

- [ ] **邀请链接接口**
  ```bash
  # 测试发送邀请
  curl -X POST http://localhost:8080/api/v1/invitations/send \
    -H "Authorization: Bearer YOUR_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{"emails":["<EMAIL>"],"message":"Welcome!"}'
  
  # 测试接受邀请
  curl -X POST http://localhost:8080/api/v1/invitations/accept-by-link/TOKEN \
    -H "Content-Type: application/json" \
    -d '{"name":"Test User","email":"<EMAIL>","password":"password123"}'
  ```

### 前端构建测试
- [ ] **开发环境**
  ```bash
  npm run dev
  # 确认启动成功，无编译错误
  ```

- [ ] **生产构建**
  ```bash
  npm run build
  # 确认构建成功，无错误和警告
  ```

- [ ] **类型检查**
  ```bash
  npm run type-check
  # 确认无TypeScript错误
  ```

## 📱 用户体验测试

### 界面测试
- [ ] **响应式设计**
  - 测试移动端显示
  - 测试平板端显示
  - 确认各种屏幕尺寸下正常

- [ ] **交互体验**
  - 确认按钮点击反馈
  - 确认加载状态显示
  - 确认错误提示清晰

- [ ] **无障碍访问**
  - 确认键盘导航正常
  - 确认屏幕阅读器兼容
  - 确认颜色对比度合适

### 性能测试
- [ ] **页面加载**
  - 测试首次加载时间
  - 测试页面切换速度
  - 确认无明显卡顿

- [ ] **内存使用**
  - 长时间使用后检查内存
  - 确认无内存泄漏
  - 测试大量数据处理

## 🚀 部署前检查

### 环境配置
- [ ] **开发环境**
  - 确认调试功能正常
  - 确认热重载工作
  - 确认开发工具可用

- [ ] **生产环境**
  - 确认调试功能已禁用
  - 确认代码已压缩
  - 确认资源已优化

### 安全检查
- [ ] **敏感信息**
  - 确认无硬编码密钥
  - 确认无调试信息泄露
  - 确认HTTPS配置正确

- [ ] **权限控制**
  - 确认路由权限正常
  - 确认API权限正常
  - 确认数据访问控制

## 📋 测试报告模板

```
测试日期：____
测试人员：____
测试环境：____

功能测试结果：
□ 验证码登录：通过/失败
□ 邀请链接：通过/失败
□ 团队管理：通过/失败
□ 兼容性：通过/失败

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____

总体评价：____
```
