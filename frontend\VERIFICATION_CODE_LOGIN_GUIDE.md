# 验证码登录系统 - 前端适配指南

## 🔄 主要变化

### 1. 登录系统改造
- **移除密码登录**：登录页面不再需要密码输入
- **验证码登录**：使用邮箱 + 6位数字验证码登录
- **倒计时功能**：发送验证码后60秒内不能重复发送

### 2. 邀请链接功能
- **新增邀请链接页面**：`/invite/:token` 路由
- **支持两种场景**：现有用户直接加入 / 新用户注册后加入
- **邀请链接管理**：团队管理页面显示和复制邀请链接

### 3. 开发环境调试
- **验证码调试**：开发环境下在控制台显示调试信息
- **调试面板**：登录页面显示开发调试面板
- **快速填充**：提供验证码快速填充工具

## 📱 用户界面变化

### 登录页面 (`/user/login`)
```
原来：邮箱 + 密码
现在：邮箱 + 验证码 + 发送验证码按钮
```

**新增功能：**
- 验证码输入框（6位数字）
- 发送验证码按钮（带倒计时）
- 验证码发送状态提示

### 邀请链接页面 (`/invite/:token`)
**全新页面，支持：**
- 选择加入方式（现有用户 / 新用户）
- 新用户注册表单
- 现有用户一键加入
- 处理结果展示

### 团队管理页面
**邀请功能增强：**
- 显示邀请链接
- 复制链接到剪贴板
- 预览邀请链接
- 发送统计信息

## 🔧 技术实现

### API 接口变化

#### 新增接口
```typescript
// 发送验证码
POST /auth/send-code
{
  email: string;
  type: 'login' | 'register';
}

// 验证码登录
POST /auth/login
{
  email: string;
  code: string; // 6位数字验证码
}

// 发送邀请（返回邀请链接）
POST /invitations/send
{
  emails: string[];
  message?: string;
}

// 通过邀请链接加入
POST /invitations/accept-by-link/:token
{
  name?: string;     // 新用户必填
  email?: string;    // 新用户必填
  password?: string; // 新用户必填
  message?: string;  // 可选
}
```

#### 修改的接口
```typescript
// 登录请求（移除password，添加code）
interface LoginRequest {
  email: string;
  code: string; // 原来是 password: string
}
```

### 类型定义更新

```typescript
// 新增类型
interface SendVerificationCodeRequest {
  email: string;
  type: 'login' | 'register';
}

interface SendVerificationCodeResponse {
  success: boolean;
  message: string;
  expireMinutes?: number;
  nextSendTime?: number;
}

interface AcceptInvitationByLinkRequest {
  name?: string;
  email?: string;
  password?: string;
  message?: string;
}

interface AcceptInvitationByLinkResponse {
  success: boolean;
  teamId?: number;
  teamName?: string;
  userId?: number;
  isNewUser?: boolean;
  nextAction?: string;
  errorMessage?: string;
}

// 更新类型
interface TeamInvitationResponse {
  // ... 原有字段
  invitationLink?: string; // 新增
}
```

### 服务层更新

```typescript
// AuthService 新增方法
class AuthService {
  static async sendVerificationCode(data: SendVerificationCodeRequest): Promise<SendVerificationCodeResponse>
  // login 方法参数变化：password -> code
}

// InvitationService 新增方法
class InvitationService {
  static async sendInvitations(data: { emails: string[]; message?: string }): Promise<SendInvitationResponse>
  static async acceptInvitationByLink(token: string, data: AcceptInvitationByLinkRequest): Promise<AcceptInvitationByLinkResponse>
}
```

## 🚀 使用指南

### 开发环境验证码获取

1. **后端日志查看**
   ```bash
   # 启动后端后，查看控制台日志
   # 格式：发送验证码: email=<EMAIL>, code=123456, type=login
   ```

2. **浏览器控制台**
   ```javascript
   // 开发环境下会显示调试信息
   console.log('验证码发送成功，请查看后端日志获取验证码');
   
   // 使用调试工具快速填充验证码
   window.devHelper.quickFillVerificationCode('123456');
   ```

3. **调试面板**
   - 登录页面会显示开发调试面板
   - 提供验证码获取指引

### 邀请链接使用流程

1. **发送邀请**
   - 团队管理页面 → 邀请成员
   - 输入邮箱列表和邀请消息
   - 系统生成邀请链接

2. **复制邀请链接**
   - 邀请记录列表中点击"复制链接"
   - 或点击"预览"查看邀请页面

3. **接受邀请**
   - 访问邀请链接 `/invite/:token`
   - 选择加入方式（现有用户/新用户）
   - 完成加入流程

## 🔍 测试场景

### 验证码登录测试
1. 访问 `/user/login`
2. 输入邮箱，点击"发送验证码"
3. 查看后端日志获取验证码
4. 输入验证码，点击"登录"
5. 验证登录成功

### 邀请链接测试
1. 登录后访问团队管理页面
2. 点击"邀请成员"，输入邮箱
3. 发送邀请，复制邀请链接
4. 新窗口访问邀请链接
5. 测试新用户注册和现有用户加入

### 错误处理测试
1. 验证码错误/过期
2. 邀请链接无效/过期
3. 网络错误处理
4. 表单验证

## 📝 注意事项

1. **开发环境专用功能**
   - 验证码调试工具仅在开发环境启用
   - 生产环境需要配置真实的邮件服务

2. **安全考虑**
   - 验证码有效期5分钟
   - 最多3次验证尝试
   - 60秒重发限制

3. **用户体验**
   - 清晰的错误提示
   - 倒计时显示
   - 加载状态指示

4. **兼容性**
   - 保留注册功能（仍使用密码）
   - 邀请链接支持新老用户
   - 向后兼容现有API

## 🐛 常见问题

**Q: 收不到验证码怎么办？**
A: 开发环境下查看后端控制台日志，生产环境检查邮件服务配置。

**Q: 邀请链接无效？**
A: 检查链接格式，确认未过期（72小时），验证后端邀请服务状态。

**Q: 验证码一直提示错误？**
A: 确认输入的是6位数字，检查是否过期，查看后端日志确认验证码。

**Q: 新用户注册失败？**
A: 检查邮箱格式，密码长度（至少8位），确认邮箱未被注册。
