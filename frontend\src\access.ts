import type { TeamDetailResponse, UserProfileResponse } from '@/types/api';
import { TeamRole } from '@/types/api';

/**
 * 角色权限检查工具函数
 */
const rolePermissions = {
  canManageTeam: (role?: TeamRole) => role === TeamRole.TEAM_CREATOR,
  canManageMembers: (role?: TeamRole) => role === TeamRole.TEAM_CREATOR,
  canAccessData: (role?: TeamRole) => role === TeamRole.TEAM_CREATOR || role === TeamRole.TEAM_MEMBER,
  hasPermissionLevel: (userRole?: TeamRole, requiredRole?: TeamRole) => {
    if (!userRole || !requiredRole) return false;
    const levels = {
      [TeamRole.TEAM_CREATOR]: 100,
      [TeamRole.TEAM_MEMBER]: 10,
    };
    return levels[userRole] >= levels[requiredRole];
  },
};

/**
 * @see https://umijs.org/docs/max/access#access
 * */
export default function access(
  initialState:
    | {
        currentUser?: UserProfileResponse;
        currentTeam?: TeamDetailResponse;
      }
    | undefined,
) {
  const { currentUser, currentTeam } = initialState ?? {};

  // 获取当前用户在团队中的角色，如果没有role字段则根据isCreator推断
  const currentRole = currentTeam?.role || (currentTeam?.isCreator ? TeamRole.TEAM_CREATOR : TeamRole.TEAM_MEMBER);

  return {
    // 基于角色的权限控制
    canManageTeam: currentUser && currentTeam && rolePermissions.canManageTeam(currentRole),
    canManageMembers: currentUser && currentTeam && rolePermissions.canManageMembers(currentRole),
    canAccessData: currentUser && currentTeam && rolePermissions.canAccessData(currentRole),

    // 向后兼容的权限控制
    canAdmin: currentUser && currentTeam && currentTeam.isCreator,

    // 基础权限
    isLoggedIn: !!currentUser,
    hasTeam: !!currentTeam,

    // 角色检查
    isTeamCreator: currentRole === TeamRole.TEAM_CREATOR,
    isTeamMember: currentRole === TeamRole.TEAM_MEMBER,

    // 权限级别检查函数
    hasPermissionLevel: (requiredRole: TeamRole) =>
      rolePermissions.hasPermissionLevel(currentRole, requiredRole),
  };
}
