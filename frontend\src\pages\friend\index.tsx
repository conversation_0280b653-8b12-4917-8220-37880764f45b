/**
 * 好友管理页面
 */

import {
  InboxOutlined,
  TeamOutlined,
  UnorderedListOutlined,
  UserAddOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Button, Card, message, Space, Tabs, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { FriendService } from '@/services';
import type { FriendWithRemark } from '@/types/api';
import AddFriend from './components/AddFriend';
// 导入组件
import FriendList from './components/FriendList';
import FriendRequests from './components/FriendRequests';

const { Title } = Typography;

const FriendManagePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('list');
  const [loading, setLoading] = useState(false);
  const [friends, setFriends] = useState<FriendWithRemark[]>([]);

  /**
   * 获取好友列表（包含备注信息）
   *
   * 功能说明：
   * 1. 请求好友列表，包含备注信息
   * 2. 更新组件状态，触发UI重新渲染
   * 3. 统一的错误处理和加载状态管理
   */
  const fetchFriends = async () => {
    try {
      setLoading(true);
      const friendList = await FriendService.getFriends();
      setFriends(friendList);
    } catch (error) {
      console.error('获取好友列表失败:', error);
      message.error('获取好友列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFriends();
  }, []);

  /**
   * 添加好友成功的回调处理
   *
   * 执行步骤：
   * 1. 显示成功提示消息
   * 2. 刷新好友列表数据
   * 3. 自动切换到好友列表标签页，方便用户查看新添加的好友
   */
  const handleAddFriendSuccess = () => {
    message.success('好友添加成功');
    fetchFriends(); // 刷新好友列表
    setActiveTab('list'); // 切换到好友列表
  };

  /**
   * 删除好友成功的回调处理
   *
   * 执行步骤：
   * 1. 显示成功提示消息
   * 2. 刷新好友列表数据，移除已删除的好友
   */
  const handleRemoveFriendSuccess = () => {
    message.success('好友删除成功');
    fetchFriends(); // 刷新好友列表
  };

  const tabItems = [
    {
      key: 'list',
      label: (
        <Space>
          <UnorderedListOutlined />
          我的好友 ({friends.length})
        </Space>
      ),
      children: (
        <FriendList
          friends={friends}
          loading={loading}
          onRemoveFriend={handleRemoveFriendSuccess}
          onRefresh={fetchFriends}
        />
      ),
    },
    {
      key: 'add',
      label: (
        <Space>
          <UserAddOutlined />
          添加好友
        </Space>
      ),
      children: <AddFriend onAddSuccess={handleAddFriendSuccess} />,
    },
    {
      key: 'requests',
      label: (
        <Space>
          <InboxOutlined />
          好友请求
        </Space>
      ),
      children: <FriendRequests onRequestHandled={fetchFriends} />,
    },
  ];

  return (
    <PageContainer
      title="好友管理"
      subTitle="管理您的好友关系，邀请好友加入团队"
      extra={[
        <Button key="refresh" onClick={fetchFriends} loading={loading}>
          刷新
        </Button>,
      ]}
    >
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size="large"
        />
      </Card>
    </PageContainer>
  );
};

export default FriendManagePage;
