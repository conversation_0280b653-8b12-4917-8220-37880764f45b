# 生产环境部署检查清单

## 🔒 安全配置

### JWT 配置
- [ ] **JWT密钥**: 使用强随机密钥，长度至少256位
- [ ] **密钥管理**: 通过环境变量 `JWT_SECRET` 配置，不在代码中硬编码
- [ ] **Token过期时间**: 根据业务需求调整，建议不超过24小时
- [ ] **Token刷新机制**: 确保刷新Token功能正常工作

### 数据库安全
- [ ] **数据库密码**: 使用强密码，通过环境变量配置
- [ ] **连接加密**: 启用SSL/TLS连接
- [ ] **访问控制**: 限制数据库访问IP范围
- [ ] **备份策略**: 配置定期数据备份

### 验证码安全
- [ ] **验证码强度**: 使用SecureRandom生成6位数字验证码
- [ ] **过期时间**: 5分钟过期时间合理
- [ ] **重发限制**: 60秒重发间隔防止滥用
- [ ] **尝试次数**: 最多3次验证尝试

### 邀请链接安全
- [ ] **令牌复杂度**: 包含随机数和时间戳的Base64编码
- [ ] **链接过期**: 72小时过期时间
- [ ] **一次性使用**: 使用后立即失效

## 🌐 网络和CORS配置

### CORS设置
```yaml
# 生产环境CORS配置示例
cors:
  allowed-origins: 
    - "https://yourdomain.com"
    - "https://app.yourdomain.com"
  allowed-methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
  allowed-headers: ["*"]
  allow-credentials: true
```

### HTTPS配置
- [ ] **SSL证书**: 配置有效的SSL证书
- [ ] **HSTS**: 启用HTTP严格传输安全
- [ ] **重定向**: HTTP自动重定向到HTTPS

## 📊 监控和日志

### 应用监控
- [ ] **健康检查**: 配置 `/actuator/health` 端点
- [ ] **指标收集**: 启用 `/actuator/metrics` 
- [ ] **性能监控**: 集成APM工具（如Micrometer）

### 日志配置
```yaml
# 生产环境日志配置
logging:
  level:
    com.teammanage: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: /var/log/teammanage/application.log
    max-size: 100MB
    max-history: 30
```

### 关键业务日志
- [ ] **登录日志**: 记录登录成功/失败
- [ ] **验证码日志**: 记录发送和验证（不记录验证码内容）
- [ ] **邀请日志**: 记录邀请发送和接受
- [ ] **错误日志**: 记录所有异常和错误

## 🗄️ 数据库配置

### 连接池配置
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
```

### 性能优化
- [ ] **索引优化**: 为常用查询字段添加索引
- [ ] **查询优化**: 检查慢查询日志
- [ ] **连接池**: 根据并发量调整连接池大小

## 🚀 性能配置

### JVM参数
```bash
# 生产环境JVM参数示例
-Xms2g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/var/log/teammanage/
```

### 缓存配置
- [ ] **Caffeine缓存**: 根据内存大小调整缓存容量
- [ ] **过期策略**: 验证缓存过期时间设置
- [ ] **监控**: 监控缓存命中率和内存使用

## 🔧 环境变量配置

### 必需的环境变量
```bash
# 数据库配置
DB_URL=**********************************************
DB_USERNAME=teammanage_user
DB_PASSWORD=your_secure_password

# JWT配置
JWT_SECRET=your_256_bit_secret_key_here

# 邀请配置
INVITATION_BASE_URL=https://app.yourdomain.com

# 邮件配置（如果集成邮件服务）
MAIL_HOST=smtp.yourdomain.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_mail_password
```

## 📧 邮件服务集成

### SMTP配置
```yaml
spring:
  mail:
    host: ${MAIL_HOST}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
```

### 邮件模板
- [ ] **验证码邮件**: 设计专业的验证码邮件模板
- [ ] **邀请邮件**: 设计团队邀请邮件模板
- [ ] **发送限制**: 配置邮件发送频率限制

## 🔍 健康检查

### 自定义健康检查
```java
@Component
public class CustomHealthIndicator implements HealthIndicator {
    @Override
    public Health health() {
        // 检查数据库连接
        // 检查缓存状态
        // 检查外部服务
        return Health.up().build();
    }
}
```

## 🛡️ 安全加固

### 应用安全
- [ ] **依赖扫描**: 定期扫描依赖漏洞
- [ ] **安全头**: 配置安全HTTP头
- [ ] **输入验证**: 验证所有用户输入
- [ ] **SQL注入**: 使用参数化查询

### 服务器安全
- [ ] **防火墙**: 配置适当的防火墙规则
- [ ] **系统更新**: 保持系统和依赖最新
- [ ] **访问控制**: 限制服务器访问权限

## 📈 容量规划

### 性能基准
- [ ] **并发用户**: 测试支持的并发用户数
- [ ] **响应时间**: 确保API响应时间<200ms
- [ ] **吞吐量**: 测试每秒请求处理能力
- [ ] **内存使用**: 监控内存使用情况

### 扩展策略
- [ ] **水平扩展**: 支持多实例部署
- [ ] **负载均衡**: 配置负载均衡器
- [ ] **数据库扩展**: 考虑读写分离

## ✅ 部署前检查

### 功能测试
- [ ] **验证码登录**: 完整流程测试
- [ ] **邀请链接**: 新用户和现有用户场景
- [ ] **权限控制**: 验证所有权限检查
- [ ] **错误处理**: 测试各种错误场景

### 性能测试
- [ ] **压力测试**: 模拟高并发场景
- [ ] **内存泄漏**: 长时间运行测试
- [ ] **数据库性能**: 大数据量测试

### 安全测试
- [ ] **渗透测试**: 进行安全渗透测试
- [ ] **OWASP检查**: 按OWASP Top 10检查
- [ ] **依赖扫描**: 扫描已知漏洞

## 🚨 应急预案

### 故障恢复
- [ ] **备份恢复**: 测试数据备份恢复流程
- [ ] **回滚计划**: 准备应用回滚方案
- [ ] **监控告警**: 配置关键指标告警

### 联系信息
- [ ] **运维团队**: 准备24/7运维联系方式
- [ ] **开发团队**: 准备开发团队联系方式
- [ ] **业务团队**: 准备业务负责人联系方式
