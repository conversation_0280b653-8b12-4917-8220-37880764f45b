{"version": 3, "sources": ["src/pages/invite/[token].tsx"], "sourcesContent": ["/**\n * 邀请链接处理页面\n * 路由: /invite/:token\n */\n\nimport { LockOutlined, MailOutlined, UserOutlined, TeamOutlined } from '@ant-design/icons';\nimport { Helmet, history, useParams } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Form,\n  Input,\n  message,\n  Space,\n  Typography,\n  Result,\n  Spin,\n  Alert,\n} from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState, useEffect } from 'react';\nimport { Footer } from '@/components';\nimport { InvitationService } from '@/services';\nimport type { AcceptInvitationByLinkRequest } from '@/types/api';\nimport Settings from '../../../config/defaultSettings';\n\nconst { Title, Text, Paragraph } = Typography;\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    inviteCard: {\n      width: '100%',\n      maxWidth: 500,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n  };\n});\n\nconst InvitePage: React.FC = () => {\n  const { token } = useParams<{ token: string }>();\n  const [loading, setLoading] = useState(false);\n  const [processing, setProcessing] = useState(false);\n  const [result, setResult] = useState<any>(null);\n  const [isNewUser, setIsNewUser] = useState<boolean | null>(null);\n  const { styles } = useStyles();\n\n  // 自定义邮箱验证函数\n  const validateEmail = (email: string) => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n\n  // 处理邀请接受（现有用户）\n  const handleAcceptAsExistingUser = async () => {\n    if (!token) return;\n    \n    setProcessing(true);\n    try {\n      const response = await InvitationService.acceptInvitationByLink(token, {});\n      \n      if (response.success) {\n        setResult({\n          type: 'success',\n          title: '加入成功！',\n          message: response.nextAction || '您已成功加入团队',\n          teamName: response.teamName,\n          isNewUser: response.isNewUser,\n        });\n      } else {\n        setResult({\n          type: 'error',\n          title: '加入失败',\n          message: response.errorMessage || '处理邀请时发生错误',\n        });\n      }\n    } catch (error) {\n      console.error('处理邀请失败:', error);\n      setResult({\n        type: 'error',\n        title: '加入失败',\n        message: '网络错误，请稍后重试',\n      });\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // 处理新用户注册并加入\n  const handleRegisterAndJoin = async (values: AcceptInvitationByLinkRequest) => {\n    if (!token) return;\n    \n    setProcessing(true);\n    try {\n      const response = await InvitationService.acceptInvitationByLink(token, values);\n      \n      if (response.success) {\n        setResult({\n          type: 'success',\n          title: '注册并加入成功！',\n          message: response.nextAction || '您已成功注册并加入团队',\n          teamName: response.teamName,\n          isNewUser: response.isNewUser,\n        });\n      } else {\n        setResult({\n          type: 'error',\n          title: '注册失败',\n          message: response.errorMessage || '注册时发生错误',\n        });\n      }\n    } catch (error) {\n      console.error('注册失败:', error);\n      setResult({\n        type: 'error',\n        title: '注册失败',\n        message: '网络错误，请稍后重试',\n      });\n    } finally {\n      setProcessing(false);\n    }\n  };\n\n  // 新用户注册表单\n  const NewUserForm = () => (\n    <Form\n      name=\"newUserJoin\"\n      size=\"large\"\n      onFinish={handleRegisterAndJoin}\n      autoComplete=\"off\"\n      layout=\"vertical\"\n    >\n      <Alert\n        message=\"您需要注册一个新账号来加入团队\"\n        type=\"info\"\n        showIcon\n        style={{ marginBottom: 24 }}\n      />\n      \n      <Form.Item\n        label=\"姓名\"\n        name=\"name\"\n        rules={[\n          { required: true, message: '请输入您的姓名！' },\n          { max: 100, message: '姓名长度不能超过100字符！' },\n        ]}\n      >\n        <Input\n          prefix={<UserOutlined />}\n          placeholder=\"请输入您的姓名\"\n        />\n      </Form.Item>\n\n      <Form.Item\n        label=\"邮箱\"\n        name=\"email\"\n        rules={[\n          { required: true, message: '请输入邮箱！' },\n          {\n            validator: (_, value) => {\n              if (!value || validateEmail(value)) {\n                return Promise.resolve();\n              }\n              return Promise.reject(new Error('请输入有效的邮箱地址！'));\n            },\n          },\n        ]}\n      >\n        <Input\n          prefix={<MailOutlined />}\n          placeholder=\"请输入您的邮箱\"\n        />\n      </Form.Item>\n\n      <Form.Item\n        label=\"密码\"\n        name=\"password\"\n        rules={[\n          { required: true, message: '请输入密码！' },\n          { min: 8, message: '密码长度至少8位！' },\n        ]}\n      >\n        <Input.Password\n          prefix={<LockOutlined />}\n          placeholder=\"请设置密码（至少8位）\"\n        />\n      </Form.Item>\n\n      <Form.Item\n        label=\"留言（可选）\"\n        name=\"message\"\n      >\n        <Input.TextArea\n          placeholder=\"向团队说点什么...\"\n          rows={3}\n          maxLength={200}\n        />\n      </Form.Item>\n\n      <Form.Item>\n        <Button type=\"primary\" htmlType=\"submit\" loading={processing} block size=\"large\">\n          注册并加入团队\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n\n  // 现有用户加入界面\n  const ExistingUserJoin = () => (\n    <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n      <TeamOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 24 }} />\n      <Title level={3}>欢迎加入团队！</Title>\n      <Paragraph type=\"secondary\">\n        检测到您已有账号，点击下方按钮即可加入团队\n      </Paragraph>\n      <Button \n        type=\"primary\" \n        size=\"large\" \n        loading={processing}\n        onClick={handleAcceptAsExistingUser}\n        style={{ marginTop: 16 }}\n      >\n        加入团队\n      </Button>\n    </div>\n  );\n\n  // 结果展示\n  const ResultDisplay = () => {\n    if (!result) return null;\n\n    return (\n      <Result\n        status={result.type}\n        title={result.title}\n        subTitle={result.message}\n        extra={[\n          <Button type=\"primary\" key=\"login\" onClick={() => history.push('/user/login')}>\n            前往登录\n          </Button>,\n          <Button key=\"home\" onClick={() => history.push('/')}>\n            返回首页\n          </Button>,\n        ]}\n      />\n    );\n  };\n\n  // 检查token有效性（这里简化处理，实际可以调用API验证）\n  useEffect(() => {\n    if (!token) {\n      setResult({\n        type: 'error',\n        title: '邀请链接无效',\n        message: '邀请链接格式错误或已过期',\n      });\n    }\n  }, [token]);\n\n  if (result) {\n    return (\n      <div className={styles.container}>\n        <Helmet>\n          <title>\n            团队邀请\n            {Settings.title && ` - ${Settings.title}`}\n          </title>\n        </Helmet>\n        <div className={styles.content}>\n          <Card className={styles.inviteCard}>\n            <ResultDisplay />\n          </Card>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          团队邀请\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队邀请</Title>\n              <Text type=\"secondary\">加入团队，开始协作</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.inviteCard}>\n          {isNewUser === null ? (\n            <div style={{ textAlign: 'center', padding: '40px 20px' }}>\n              <Title level={4}>选择加入方式</Title>\n              <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n                <Button \n                  type=\"primary\" \n                  size=\"large\" \n                  block\n                  onClick={() => setIsNewUser(false)}\n                >\n                  我已有账号，直接加入\n                </Button>\n                <Button \n                  size=\"large\" \n                  block\n                  onClick={() => setIsNewUser(true)}\n                >\n                  我是新用户，注册后加入\n                </Button>\n              </Space>\n            </div>\n          ) : isNewUser ? (\n            <NewUserForm />\n          ) : (\n            <ExistingUserJoin />\n          )}\n          \n          {isNewUser !== null && (\n            <div style={{ textAlign: 'center', marginTop: 16 }}>\n              <Button type=\"link\" onClick={() => setIsNewUser(null)}>\n                返回选择\n              </Button>\n            </div>\n          )}\n        </Card>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default InvitePage;\n"], "names": [], "mappings": ";;;AAAA;;;CAGC;;;;4BAoXD;;;eAAA;;;;;;;8BAlXuE;4BAC5B;6BAYpC;kCACsB;wEACc;mCACpB;iCACW;iFAEb;;;;;;;;;;AAErB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAE7C,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;IACvC,OAAO;QACL,WAAW;YACT,SAAS;YACT,eAAe;YACf,QAAQ;YACR,UAAU;YACV,iBACE;YACF,gBAAgB;QAClB;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,cAAc;YACd,WAAW;QACb;QACA,MAAM;YACJ,cAAc;QAChB;QACA,OAAO;YACL,cAAc;QAChB;QACA,YAAY;YACV,OAAO;YACP,UAAU;YACV,WAAW,MAAM,iBAAiB;QACpC;QACA,QAAQ;YACN,WAAW;YACX,WAAW;QACb;IACF;AACF;AAEA,MAAM,aAAuB;;IAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,cAAS;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,eAAQ,EAAM;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAiB;IAC3D,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,YAAY;IACZ,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa;QACnB,OAAO,WAAW,IAAI,CAAC;IACzB;IAEA,eAAe;IACf,MAAM,6BAA6B;QACjC,IAAI,CAAC,OAAO;QAEZ,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,2BAAiB,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAExE,IAAI,SAAS,OAAO,EAClB,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS,SAAS,UAAU,IAAI;gBAChC,UAAU,SAAS,QAAQ;gBAC3B,WAAW,SAAS,SAAS;YAC/B;iBAEA,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS,SAAS,YAAY,IAAI;YACpC;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,aAAa;IACb,MAAM,wBAAwB,OAAO;QACnC,IAAI,CAAC,OAAO;QAEZ,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,2BAAiB,CAAC,sBAAsB,CAAC,OAAO;YAEvE,IAAI,SAAS,OAAO,EAClB,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS,SAAS,UAAU,IAAI;gBAChC,UAAU,SAAS,QAAQ;gBAC3B,WAAW,SAAS,SAAS;YAC/B;iBAEA,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS,SAAS,YAAY,IAAI;YACpC;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,UAAU;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,UAAU;IACV,MAAM,cAAc,kBAClB,2BAAC,UAAI;YACH,MAAK;YACL,MAAK;YACL,UAAU;YACV,cAAa;YACb,QAAO;;8BAEP,2BAAC,WAAK;oBACJ,SAAQ;oBACR,MAAK;oBACL,QAAQ;oBACR,OAAO;wBAAE,cAAc;oBAAG;;;;;;8BAG5B,2BAAC,UAAI,CAAC,IAAI;oBACR,OAAM;oBACN,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAW;wBACtC;4BAAE,KAAK;4BAAK,SAAS;wBAAiB;qBACvC;8BAED,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;;;;;;;;;;;8BAIhB,2BAAC,UAAI,CAAC,IAAI;oBACR,OAAM;oBACN,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC;4BACE,WAAW,CAAC,GAAG;gCACb,IAAI,CAAC,SAAS,cAAc,QAC1B,OAAO,QAAQ,OAAO;gCAExB,OAAO,QAAQ,MAAM,CAAC,IAAI,MAAM;4BAClC;wBACF;qBACD;8BAED,cAAA,2BAAC,WAAK;wBACJ,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;;;;;;;;;;;8BAIhB,2BAAC,UAAI,CAAC,IAAI;oBACR,OAAM;oBACN,MAAK;oBACL,OAAO;wBACL;4BAAE,UAAU;4BAAM,SAAS;wBAAS;wBACpC;4BAAE,KAAK;4BAAG,SAAS;wBAAY;qBAChC;8BAED,cAAA,2BAAC,WAAK,CAAC,QAAQ;wBACb,sBAAQ,2BAAC,mBAAY;;;;;wBACrB,aAAY;;;;;;;;;;;8BAIhB,2BAAC,UAAI,CAAC,IAAI;oBACR,OAAM;oBACN,MAAK;8BAEL,cAAA,2BAAC,WAAK,CAAC,QAAQ;wBACb,aAAY;wBACZ,MAAM;wBACN,WAAW;;;;;;;;;;;8BAIf,2BAAC,UAAI,CAAC,IAAI;8BACR,cAAA,2BAAC,YAAM;wBAAC,MAAK;wBAAU,UAAS;wBAAS,SAAS;wBAAY,KAAK;wBAAC,MAAK;kCAAQ;;;;;;;;;;;;;;;;;IAOvF,WAAW;IACX,MAAM,mBAAmB,kBACvB,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAY;;8BACtD,2BAAC,mBAAY;oBAAC,OAAO;wBAAE,UAAU;wBAAI,OAAO;wBAAW,cAAc;oBAAG;;;;;;8BACxE,2BAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,2BAAC;oBAAU,MAAK;8BAAY;;;;;;8BAG5B,2BAAC,YAAM;oBACL,MAAK;oBACL,MAAK;oBACL,SAAS;oBACT,SAAS;oBACT,OAAO;wBAAE,WAAW;oBAAG;8BACxB;;;;;;;;;;;;IAML,OAAO;IACP,MAAM,gBAAgB;QACpB,IAAI,CAAC,QAAQ,OAAO;QAEpB,qBACE,2BAAC,YAAM;YACL,QAAQ,OAAO,IAAI;YACnB,OAAO,OAAO,KAAK;YACnB,UAAU,OAAO,OAAO;YACxB,OAAO;8BACL,2BAAC,YAAM;oBAAC,MAAK;oBAAsB,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;8BAAgB;mBAApD;;;;;8BAG3B,2BAAC,YAAM;oBAAY,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;8BAAM;mBAAzC;;;;;aAGb;;;;;;IAGP;IAEA,iCAAiC;IACjC,IAAA,gBAAS,EAAC;QACR,IAAI,CAAC,OACH,UAAU;YACR,MAAM;YACN,OAAO;YACP,SAAS;QACX;IAEJ,GAAG;QAAC;KAAM;IAEV,IAAI,QACF,qBACE,2BAAC;QAAI,WAAW,OAAO,SAAS;;0BAC9B,2BAAC,WAAM;0BACL,cAAA,2BAAC;;wBAAM;wBAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;0BAG7C,2BAAC;gBAAI,WAAW,OAAO,OAAO;0BAC5B,cAAA,2BAAC,UAAI;oBAAC,WAAW,OAAO,UAAU;8BAChC,cAAA,2BAAC;;;;;;;;;;;;;;;0BAGL,2BAAC,kBAAM;;;;;;;;;;;IAKb,qBACE,2BAAC;QAAI,WAAW,OAAO,SAAS;;0BAC9B,2BAAC,WAAM;0BACL,cAAA,2BAAC;;wBAAM;wBAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;0BAG7C,2BAAC;gBAAI,WAAW,OAAO,OAAO;;kCAC5B,2BAAC;wBAAI,WAAW,OAAO,MAAM;kCAC3B,cAAA,2BAAC,WAAK;4BAAC,WAAU;4BAAW,OAAM;4BAAS,MAAK;;8CAC9C,2BAAC;oCAAI,WAAW,OAAO,IAAI;8CACzB,cAAA,2BAAC;wCAAI,KAAI;wCAAY,KAAI;wCAAW,QAAQ;;;;;;;;;;;8CAE9C,2BAAC;oCAAI,WAAW,OAAO,KAAK;;sDAC1B,2BAAC;4CAAM,OAAO;sDAAG;;;;;;sDACjB,2BAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;;;;;;kCAK7B,2BAAC,UAAI;wBAAC,WAAW,OAAO,UAAU;;4BAC/B,cAAc,qBACb,2BAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAU,SAAS;gCAAY;;kDACtD,2BAAC;wCAAM,OAAO;kDAAG;;;;;;kDACjB,2BAAC,WAAK;wCAAC,WAAU;wCAAW,MAAK;wCAAQ,OAAO;4CAAE,OAAO;wCAAO;;0DAC9D,2BAAC,YAAM;gDACL,MAAK;gDACL,MAAK;gDACL,KAAK;gDACL,SAAS,IAAM,aAAa;0DAC7B;;;;;;0DAGD,2BAAC,YAAM;gDACL,MAAK;gDACL,KAAK;gDACL,SAAS,IAAM,aAAa;0DAC7B;;;;;;;;;;;;;;;;;uCAKH,0BACF,2BAAC;;;;qDAED,2BAAC;;;;;4BAGF,cAAc,sBACb,2BAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAU,WAAW;gCAAG;0CAC/C,cAAA,2BAAC,YAAM;oCAAC,MAAK;oCAAO,SAAS,IAAM,aAAa;8CAAO;;;;;;;;;;;;;;;;;kCAO7D,2BAAC;wBAAI,WAAW,OAAO,MAAM;kCAC3B,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;;;;;;0BAG3B,2BAAC,kBAAM;;;;;;;;;;;AAGb;GAhTM;;QACc,cAAS;QAKR;;;KANf;IAkTN,WAAe"}