/**
 * 用户邀请页面
 * 
 * 功能特性：
 * - 显示用户收到的所有邀请
 * - 支持接受或拒绝邀请
 * - 显示邀请详情和团队信息
 * - 自动刷新待处理邀请
 */

import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import {
  Card,
  Table,
  Button,
  Space,
  message,
  Modal,
  Form,
  Input,
  Typography,
  Tag,
  Tooltip,
  Avatar,
} from 'antd';
import {
  CheckOutlined,
  CloseOutlined,
  TeamOutlined,
  UserOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

// 导入服务和类型
import { InvitationService } from '@/services';
import type { TeamInvitationResponse, RespondInvitationRequest } from '@/types/api';
import { InvitationStatus } from '@/types/api';
import InvitationStatusComponent from '@/components/InvitationStatus';

const { Text, Title } = Typography;
const { TextArea } = Input;

const UserInvitationsPage: React.FC = () => {
  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [respondModalVisible, setRespondModalVisible] = useState(false);
  const [selectedInvitation, setSelectedInvitation] = useState<TeamInvitationResponse | null>(null);
  const [respondForm] = Form.useForm();

  // 获取邀请列表
  const fetchInvitations = async () => {
    try {
      setLoading(true);
      const invitationList = await InvitationService.getUserReceivedInvitations();
      setInvitations(invitationList || []);
    } catch (error) {
      console.error('获取邀请列表失败:', error);
      message.error('获取邀请列表失败');
      setInvitations([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInvitations();
  }, []);

  // 响应邀请
  const handleRespondInvitation = async (invitation: TeamInvitationResponse, accept: boolean) => {
    setSelectedInvitation(invitation);
    respondForm.setFieldsValue({ accept });
    setRespondModalVisible(true);
  };

  // 提交响应
  const handleSubmitResponse = async (values: { accept: boolean; message?: string }) => {
    if (!selectedInvitation) return;

    try {
      const request: RespondInvitationRequest = {
        accept: values.accept,
        message: values.message,
      };

      await InvitationService.respondToInvitation(selectedInvitation.id, request);
      
      const action = values.accept ? '接受' : '拒绝';
      message.success(`邀请${action}成功`);
      
      setRespondModalVisible(false);
      respondForm.resetFields();
      setSelectedInvitation(null);
      fetchInvitations();
    } catch (error) {
      console.error('响应邀请失败:', error);
      message.error('响应邀请失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<TeamInvitationResponse> = [
    {
      title: '团队信息',
      key: 'team',
      render: (_, record) => (
        <Space>
          <Avatar icon={<TeamOutlined />} />
          <div>
            <Text strong>{record.teamName}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              团队ID: {record.teamId}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '邀请人',
      key: 'inviter',
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <div>
            <Text strong>{record.inviterName}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.inviterEmail}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '邀请状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <InvitationStatusComponent 
          status={status} 
          isExpired={record.isExpired} 
        />
      ),
    },
    {
      title: '邀请时间',
      dataIndex: 'invitedAt',
      key: 'invitedAt',
      render: (time) => (
        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>
          {dayjs(time).format('MM-DD HH:mm')}
        </Tooltip>
      ),
      sorter: (a, b) => dayjs(a.invitedAt).unix() - dayjs(b.invitedAt).unix(),
      defaultSortOrder: 'descend',
    },
    {
      title: '过期时间',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      render: (time, record) => {
        const isExpired = record.isExpired;
        return (
          <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>
            <Text type={isExpired ? 'danger' : 'secondary'}>
              {dayjs(time).format('MM-DD HH:mm')}
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {record.canBeResponded && (
            <>
              <Button
                type="primary"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleRespondInvitation(record, true)}
              >
                接受
              </Button>
              <Button
                size="small"
                icon={<CloseOutlined />}
                onClick={() => handleRespondInvitation(record, false)}
              >
                拒绝
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  // 统计信息
  const pendingCount = invitations.filter(inv => inv.status === InvitationStatus.PENDING && !inv.isExpired).length;
  const acceptedCount = invitations.filter(inv => inv.status === InvitationStatus.ACCEPTED).length;
  const rejectedCount = invitations.filter(inv => inv.status === InvitationStatus.REJECTED).length;

  return (
    <PageContainer
      title="我的邀请"
      subTitle="管理您收到的团队邀请"
      extra={[
        <Button 
          key="refresh"
          icon={<ReloadOutlined />} 
          onClick={fetchInvitations}
          loading={loading}
        >
          刷新
        </Button>
      ]}
    >
      {/* 统计卡片 */}
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card>
          <Space size="large">
            <div>
              <Text type="secondary">待处理邀请</Text>
              <br />
              <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {pendingCount}
              </Text>
            </div>
            <div>
              <Text type="secondary">已接受</Text>
              <br />
              <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {acceptedCount}
              </Text>
            </div>
            <div>
              <Text type="secondary">已拒绝</Text>
              <br />
              <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>
                {rejectedCount}
              </Text>
            </div>
          </Space>
        </Card>

        {/* 邀请列表 */}
        <Card title="邀请列表">
          <Table
            columns={columns}
            dataSource={invitations}
            rowKey="id"
            loading={loading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条邀请记录`,
              pageSize: 10,
            }}
          />
        </Card>
      </Space>

      {/* 响应邀请弹窗 */}
      <Modal
        title={`${respondForm.getFieldValue('accept') ? '接受' : '拒绝'}邀请`}
        open={respondModalVisible}
        onCancel={() => {
          setRespondModalVisible(false);
          respondForm.resetFields();
          setSelectedInvitation(null);
        }}
        footer={null}
        width={500}
      >
        {selectedInvitation && (
          <div style={{ marginBottom: 16 }}>
            <Text strong>团队：</Text> {selectedInvitation.teamName}
            <br />
            <Text strong>邀请人：</Text> {selectedInvitation.inviterName}
            <br />
            {selectedInvitation.message && (
              <>
                <Text strong>邀请消息：</Text>
                <div style={{ 
                  background: '#f5f5f5', 
                  padding: 8, 
                  borderRadius: 4, 
                  marginTop: 4 
                }}>
                  {selectedInvitation.message}
                </div>
              </>
            )}
          </div>
        )}
        
        <Form
          form={respondForm}
          layout="vertical"
          onFinish={handleSubmitResponse}
        >
          <Form.Item name="accept" hidden>
            <Input />
          </Form.Item>
          
          <Form.Item
            name="message"
            label="回复消息（可选）"
          >
            <TextArea
              rows={3}
              placeholder="您可以添加一些回复消息..."
              maxLength={200}
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                htmlType="submit"
                icon={respondForm.getFieldValue('accept') ? <CheckOutlined /> : <CloseOutlined />}
              >
                确认{respondForm.getFieldValue('accept') ? '接受' : '拒绝'}
              </Button>
              <Button onClick={() => setRespondModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default UserInvitationsPage;
