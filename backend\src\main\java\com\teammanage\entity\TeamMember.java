package com.teammanage.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.teammanage.enums.TeamRole;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 团队成员实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

@TableName("team_member")
public class TeamMember extends BaseEntity {

    /**
     * 团队ID
     */
    private Long teamId;

    /**
     * 用户ID
     */
    private Long accountId;

    /**
     * 是否为创建者
     * 与 role 字段共存，用于不同的业务场景
     */
    private Boolean isCreator;

    /**
     * 团队角色
     * 与 isCreator 字段共存，用于不同的业务场景
     */
    private TeamRole role;

    /**
     * 分配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignedAt;

    /**
     * 最后访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessTime;

    /**
     * 账号状态
     */
    private Boolean isActive;

    /**
     * 删除标记
     */
    @TableLogic
    private Boolean isDeleted;

    // 手动添加getter/setter方法
    public Long getTeamId() { return teamId; }
    public void setTeamId(Long teamId) { this.teamId = teamId; }

    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }

    /**
     * 获取是否为创建者
     * 与 getRole() 方法共存，用于不同的业务场景
     */
    public Boolean getIsCreator() { return isCreator; }

    /**
     * 设置是否为创建者
     * 与 setRole() 方法共存，用于不同的业务场景
     */
    public void setIsCreator(Boolean isCreator) {
        this.isCreator = isCreator;
        // 同步更新role字段以保持一致性
        this.role = TeamRole.fromIsCreator(Boolean.TRUE.equals(isCreator));
    }

    public TeamRole getRole() { return role; }

    public void setRole(TeamRole role) {
        this.role = role;
        // 同步更新isCreator字段以保持向后兼容性
        this.isCreator = role != null && role.toIsCreator();
    }

    public LocalDateTime getAssignedAt() { return assignedAt; }
    public void setAssignedAt(LocalDateTime assignedAt) { this.assignedAt = assignedAt; }

    public LocalDateTime getLastAccessTime() { return lastAccessTime; }
    public void setLastAccessTime(LocalDateTime lastAccessTime) { this.lastAccessTime = lastAccessTime; }

    public Boolean getIsActive() { return isActive; }
    public void setIsActive(Boolean isActive) { this.isActive = isActive; }

    public Boolean getIsDeleted() { return isDeleted; }
    public void setIsDeleted(Boolean isDeleted) { this.isDeleted = isDeleted; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        TeamMember that = (TeamMember) o;
        return Objects.equals(teamId, that.teamId) &&
               Objects.equals(accountId, that.accountId) &&
               Objects.equals(role, that.role) &&
               Objects.equals(assignedAt, that.assignedAt) &&
               Objects.equals(lastAccessTime, that.lastAccessTime) &&
               Objects.equals(isActive, that.isActive) &&
               Objects.equals(isDeleted, that.isDeleted);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), teamId, accountId, role, assignedAt, lastAccessTime, isActive, isDeleted);
    }

    // 角色相关的便利方法

    /**
     * 检查是否可以管理团队
     *
     * @return 是否可以管理团队
     */
    public boolean canManageTeam() {
        return role != null && role.canManageTeam();
    }

    /**
     * 检查是否可以管理成员
     *
     * @return 是否可以管理成员
     */
    public boolean canManageMembers() {
        return role != null && role.canManageMembers();
    }

    /**
     * 检查是否可以访问数据
     *
     * @return 是否可以访问数据
     */
    public boolean canAccessData() {
        return role != null && role.canAccessData();
    }

    /**
     * 检查角色权限是否高于或等于指定角色
     *
     * @param other 要比较的角色
     * @return 是否有更高或相等的权限
     */
    public boolean hasPermissionLevel(TeamRole other) {
        return role != null && role.hasPermissionLevel(other);
    }

}
