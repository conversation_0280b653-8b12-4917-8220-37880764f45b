/**
 * 操作按钮组件
 * 用于团队选择页面的操作按钮，包括进入团队、创建团队、退出登录
 */

import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { createStyles } from 'antd-style';
import React from 'react';

const useStyles = createStyles(() => {
  return {
    actions: {
      marginTop: 24,
      display: 'flex',
      gap: 16,
      justifyContent: 'center',
      flexWrap: 'wrap',
    },
    createTeamButton: {
      borderStyle: 'dashed',
    },
  };
});

interface ActionButtonsProps {
  hasTeams: boolean;
  selectedTeamId: number | null;
  loading: boolean;
  onTeamLogin: () => void;
  onCreateTeam: () => void;
  onLogout: () => void;
  showCreateButton?: boolean; // 控制是否显示创建团队按钮
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  hasTeams,
  selectedTeamId,
  loading,
  onTeamLogin,
  onCreateTeam,
  onLogout,
  showCreateButton = true, // 默认显示创建团队按钮
}) => {
  const { styles } = useStyles();

  return (
    <div className={styles.actions}>
      {/* 只有当有团队时才显示进入团队按钮 */}
      {hasTeams && (
        <Button
          type="primary"
          size="large"
          loading={loading}
          disabled={!selectedTeamId}
          onClick={onTeamLogin}
        >
          确认进入
        </Button>
      )}

      {/* 根据 showCreateButton 属性控制创建团队按钮的显示 */}
      {showCreateButton && (
        <Button
          size="large"
          icon={<PlusOutlined />}
          className={styles.createTeamButton}
          onClick={onCreateTeam}
        >
          创建新团队
        </Button>
      )}

      <Button size="large" onClick={onLogout}>
        退出登录
      </Button>
    </div>
  );
};

export default ActionButtons;
