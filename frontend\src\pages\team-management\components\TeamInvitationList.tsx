/**
 * 团队邀请列表组件
 * 
 * 功能特性：
 * - 显示团队发出的所有邀请记录
 * - 支持取消待处理的邀请
 * - 显示邀请状态和过期时间
 * - 支持搜索和筛选
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  message,
  Popconfirm,
  Typography,
  Tag,
  Tooltip,
} from 'antd';
import {
  SearchOutlined,
  DeleteOutlined,
  ReloadOutlined,
  MailOutlined,
  LinkOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

// 导入服务和类型
import { InvitationService } from '@/services';
import type { TeamInvitationResponse } from '@/types/api';
import { InvitationStatus } from '@/types/api';
import InvitationStatusComponent from '@/components/InvitationStatus';

const { Text } = Typography;
const { Option } = Select;

interface TeamInvitationListProps {
  onRefresh?: () => void;
}

const TeamInvitationList: React.FC<TeamInvitationListProps> = ({ onRefresh }) => {
  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');

  // 获取邀请列表
  const fetchInvitations = async () => {
    try {
      setLoading(true);
      const invitationList = await InvitationService.getCurrentTeamInvitations();
      setInvitations(invitationList || []);
    } catch (error) {
      console.error('获取邀请列表失败:', error);
      message.error('获取邀请列表失败');
      setInvitations([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInvitations();
  }, []);

  // 取消邀请
  const handleCancelInvitation = async (invitationId: number) => {
    try {
      await InvitationService.cancelInvitation(invitationId);
      message.success('邀请取消成功');
      fetchInvitations();
      onRefresh?.();
    } catch (error) {
      console.error('取消邀请失败:', error);
      message.error('取消邀请失败');
    }
  };

  // 过滤邀请列表
  const filteredInvitations = invitations.filter(invitation => {
    const matchesSearch = !searchText || 
      invitation.inviteeEmail.toLowerCase().includes(searchText.toLowerCase()) ||
      (invitation.inviteeName && invitation.inviteeName.toLowerCase().includes(searchText.toLowerCase()));
    
    const matchesStatus = !statusFilter || invitation.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // 表格列定义
  const columns: ColumnsType<TeamInvitationResponse> = [
    {
      title: '被邀请人',
      key: 'invitee',
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <Text strong>{record.inviteeName || '未注册用户'}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <MailOutlined /> {record.inviteeEmail}
          </Text>
        </Space>
      ),
    },
    {
      title: '邀请状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <InvitationStatusComponent 
          status={status} 
          isExpired={record.isExpired} 
        />
      ),
      filters: [
        { text: '待确认', value: InvitationStatus.PENDING },
        { text: '已接受', value: InvitationStatus.ACCEPTED },
        { text: '已拒绝', value: InvitationStatus.REJECTED },
        { text: '已过期', value: InvitationStatus.EXPIRED },
        { text: '已取消', value: InvitationStatus.CANCELLED },
      ],
      onFilter: (value, record) => record.status === value,
    },
    {
      title: '邀请时间',
      dataIndex: 'invitedAt',
      key: 'invitedAt',
      render: (time) => (
        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>
          {dayjs(time).format('MM-DD HH:mm')}
        </Tooltip>
      ),
      sorter: (a, b) => dayjs(a.invitedAt).unix() - dayjs(b.invitedAt).unix(),
    },
    {
      title: '过期时间',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      render: (time, record) => {
        const isExpired = record.isExpired;
        return (
          <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>
            <Text type={isExpired ? 'danger' : 'secondary'}>
              {dayjs(time).format('MM-DD HH:mm')}
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: '响应时间',
      dataIndex: 'respondedAt',
      key: 'respondedAt',
      render: (time) => time ? (
        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>
          {dayjs(time).format('MM-DD HH:mm')}
        </Tooltip>
      ) : '-',
    },
    {
      title: '邀请链接',
      key: 'invitationLink',
      render: (_, record) => {
        if (!record.invitationLink) return '-';

        const copyLink = () => {
          navigator.clipboard.writeText(record.invitationLink!).then(() => {
            message.success('邀请链接已复制到剪贴板');
          }).catch(() => {
            message.error('复制失败，请手动复制');
          });
        };

        return (
          <Space>
            <Tooltip title="复制邀请链接">
              <Button
                size="small"
                icon={<CopyOutlined />}
                onClick={copyLink}
              >
                复制链接
              </Button>
            </Tooltip>
            <Tooltip title={record.invitationLink}>
              <Button
                size="small"
                icon={<LinkOutlined />}
                onClick={() => window.open(record.invitationLink, '_blank')}
              >
                预览
              </Button>
            </Tooltip>
          </Space>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {record.canBeCancelled && (
            <Popconfirm
              title="确定要取消这个邀请吗？"
              description="取消后被邀请人将无法通过此邀请加入团队"
              onConfirm={() => handleCancelInvitation(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                size="small"
                danger
                icon={<DeleteOutlined />}
              >
                取消邀请
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="邀请记录"
      extra={
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchInvitations}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      }
    >
      {/* 搜索和筛选 */}
      <Space style={{ marginBottom: 16 }}>
        <Input
          placeholder="搜索邮箱或姓名"
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 200 }}
        />
        <Select
          placeholder="筛选状态"
          value={statusFilter}
          onChange={setStatusFilter}
          style={{ width: 120 }}
          allowClear
        >
          <Option value={InvitationStatus.PENDING}>待确认</Option>
          <Option value={InvitationStatus.ACCEPTED}>已接受</Option>
          <Option value={InvitationStatus.REJECTED}>已拒绝</Option>
          <Option value={InvitationStatus.EXPIRED}>已过期</Option>
          <Option value={InvitationStatus.CANCELLED}>已取消</Option>
        </Select>
      </Space>

      {/* 邀请列表表格 */}
      <Table
        columns={columns}
        dataSource={filteredInvitations}
        rowKey="id"
        loading={loading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条邀请记录`,
          pageSize: 10,
        }}
      />
    </Card>
  );
};

export default TeamInvitationList;
