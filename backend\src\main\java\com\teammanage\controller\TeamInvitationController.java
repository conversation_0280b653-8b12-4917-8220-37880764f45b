package com.teammanage.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.teammanage.common.ApiResponse;
import com.teammanage.context.TeamContextHolder;
import com.teammanage.dto.request.AcceptInvitationByLinkRequest;
import com.teammanage.dto.request.InviteMembersRequest;
import com.teammanage.dto.request.RespondInvitationRequest;
import com.teammanage.dto.response.AcceptInvitationByLinkResponse;
import com.teammanage.dto.response.InvitationInfoResponse;
import com.teammanage.dto.response.SendInvitationResponse;
import com.teammanage.service.TeamInvitationService;
import com.teammanage.util.SecurityUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 团队邀请控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/invitations")
@Tag(name = "团队邀请管理", description = "团队邀请相关接口")
@SecurityRequirement(name = "bearerAuth")
public class TeamInvitationController {

    private static final Logger log = LoggerFactory.getLogger(TeamInvitationController.class);

    @Autowired
    private TeamInvitationService teamInvitationService;

    /**
     * 发送邀请（需要Team Token，仅创建者）
     */
    @PostMapping("/send")
    @Operation(
        summary = "发送邀请",
        description = "创建并发送团队邀请，生成唯一的邀请链接，需要Team Token且仅创建者可操作",
        security = { @SecurityRequirement(name = "bearerAuth") }
    )
    @io.swagger.v3.oas.annotations.responses.ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "邀请发送成功",
            content = @Content(schema = @Schema(implementation = SendInvitationResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "请求参数错误"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "401",
            description = "未授权，需要Team Token"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "403",
            description = "权限不足，需要创建者权限"
        )
    })
    public ApiResponse<SendInvitationResponse> sendInvitations(
            @Parameter(description = "邀请成员请求", required = true)
            @Valid @RequestBody InviteMembersRequest request) {
        Long teamId = TeamContextHolder.getCurrentTeamId();
        Long inviterId = TeamContextHolder.getCurrentUserId();

        SendInvitationResponse response = teamInvitationService.createInvitationsWithLinks(
                teamId, inviterId, request.getEmails(), request.getMessage());

        return ApiResponse.success("邀请发送成功", response);
    }

    /**
     * 获取邀请信息（公开访问）
     */
    @GetMapping("/info/{invitationToken}")
    @Operation(summary = "获取邀请信息", description = "通过邀请令牌获取邀请详情，用于显示确认页面，无需认证")
    public ApiResponse<InvitationInfoResponse> getInvitationInfo(
            @Parameter(description = "邀请令牌") @PathVariable String invitationToken) {

        InvitationInfoResponse response = teamInvitationService.getInvitationInfo(invitationToken);

        if (response.getSuccess()) {
            return ApiResponse.success("获取邀请信息成功", response);
        } else {
            return ApiResponse.error(response.getErrorMessage());
        }
    }

    /**
     * 通过邀请链接响应邀请（公开访问）
     */
    @PostMapping("/accept-by-link/{invitationToken}")
    @Operation(summary = "通过邀请链接响应邀请", description = "处理用户通过邀请链接加入团队，无需认证")
    public ApiResponse<AcceptInvitationByLinkResponse> acceptInvitationByLink(
            @Parameter(description = "邀请令牌") @PathVariable String invitationToken,
            @RequestBody(required = false) AcceptInvitationByLinkRequest request) {

        // 如果请求体为空，创建一个空的请求对象
        if (request == null) {
            request = new AcceptInvitationByLinkRequest();
        }

        AcceptInvitationByLinkResponse response = teamInvitationService.processInvitationByToken(invitationToken, request);

        if (response.getSuccess()) {
            return ApiResponse.success("邀请接受成功", response);
        } else {
            return ApiResponse.error(response.getErrorMessage());
        }
    }

    // /**
    //  * 获取当前团队的邀请列表（需要Team Token，仅创建者）
    //  */
    // @GetMapping("/team/current")
    // @Operation(summary = "获取当前团队邀请列表", description = "获取当前团队的所有邀请记录，需要Team Token且仅创建者可查看")
    // public ApiResponse<List<TeamInvitationResponse>> getCurrentTeamInvitations() {
    //     Long teamId = TeamContextHolder.getCurrentTeamId();
    //     List<TeamInvitationResponse> invitations = teamInvitationService.getTeamInvitations(teamId);
    //     return ApiResponse.success("获取团队邀请列表成功", invitations);
    // }

    // /**
    //  * 获取用户收到的邀请列表（需要Account Token）
    //  */
    // @GetMapping("/user/received")
    // @Operation(summary = "获取用户收到的邀请列表", description = "获取当前用户收到的所有邀请记录，需要Account Token")
    // public ApiResponse<List<TeamInvitationResponse>> getUserReceivedInvitations() {
    //     String userEmail = SecurityUtil.getCurrentUserEmail();
    //     List<TeamInvitationResponse> invitations = teamInvitationService.getUserInvitations(userEmail);
    //     return ApiResponse.success("获取用户邀请列表成功", invitations);
    // }

    // /**
    //  * 获取用户收到的待处理邀请列表（需要Account Token）
    //  */
    // @GetMapping("/user/pending")
    // @Operation(summary = "获取用户待处理邀请列表", description = "获取当前用户收到的待处理邀请记录，需要Account Token")
    // public ApiResponse<List<TeamInvitationResponse>> getUserPendingInvitations() {
    //     String userEmail = SecurityUtil.getCurrentUserEmail();
    //     List<TeamInvitationResponse> invitations = teamInvitationService.getUserPendingInvitations(userEmail);
    //     return ApiResponse.success("获取用户待处理邀请列表成功", invitations);
    // }

    /**
     * 响应邀请（需要Account Token）
     */
    @PostMapping("/{invitationId}/respond")
    @Operation(summary = "响应邀请", description = "接受或拒绝团队邀请，需要Account Token")
    public ApiResponse<Void> respondToInvitation(
            @Parameter(description = "邀请ID") @PathVariable Long invitationId,
            @Valid @RequestBody RespondInvitationRequest request) {
        Long userId = SecurityUtil.getCurrentUserId();
        teamInvitationService.respondToInvitation(invitationId, userId, request.getAccept(), request.getMessage());
        
        String action = request.getAccept() ? "接受" : "拒绝";
        return ApiResponse.<Void>success("邀请" + action + "成功", null);
    }

    /**
     * 取消邀请（需要Team Token，仅邀请人）
     */
    @DeleteMapping("/{invitationId}")
    @Operation(summary = "取消邀请", description = "取消指定的团队邀请，需要Team Token且仅邀请人可操作")
    public ApiResponse<Void> cancelInvitation(
            @Parameter(description = "邀请ID") @PathVariable Long invitationId) {
        Long operatorId = TeamContextHolder.getCurrentUserId();
        teamInvitationService.cancelInvitation(invitationId, operatorId);
        return ApiResponse.<Void>success("邀请取消成功", null);
    }

    // /**
    //  * 获取邀请详情（需要Account Token或Team Token）
    //  */
    // @GetMapping("/{invitationId}")
    // @Operation(summary = "获取邀请详情", description = "获取指定邀请的详细信息")
    // public ApiResponse<TeamInvitationResponse> getInvitationDetail(
    //         @Parameter(description = "邀请ID") @PathVariable Long invitationId) {
    //     // 这里需要实现获取单个邀请详情的逻辑
    //     // 暂时返回空实现，后续可以扩展
    //     return ApiResponse.<TeamInvitationResponse>success("功能开发中", null);
    // }

    /**
     * 批量更新过期邀请状态（系统内部接口）
     */
    @PostMapping("/system/update-expired")
    @Operation(summary = "更新过期邀请状态", description = "系统内部接口，更新所有过期邀请的状态")
    public ApiResponse<Integer> updateExpiredInvitations() {
        int count = teamInvitationService.updateExpiredInvitations();
        return ApiResponse.success("过期邀请状态更新完成", count);
    }
}
