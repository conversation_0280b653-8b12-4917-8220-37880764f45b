package com.teammanage.dto.response;

/**
 * 发送验证码响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SendVerificationCodeResponse {

    /**
     * 是否发送成功
     */
    private Boolean success;

    /**
     * 消息
     */
    private String message;

    /**
     * 下次可发送时间（秒）
     */
    private Integer nextSendTime;

    /**
     * 验证码过期时间（分钟）
     */
    private Integer expireMinutes;

    /**
     * 验证码（仅开发环境）
     */
    private String debugCode;

    // Getter and Setter methods
    public Boolean getSuccess() { return success; }
    public void setSuccess(Boolean success) { this.success = success; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public Integer getNextSendTime() { return nextSendTime; }
    public void setNextSendTime(Integer nextSendTime) { this.nextSendTime = nextSendTime; }

    public Integer getExpireMinutes() { return expireMinutes; }
    public void setExpireMinutes(Integer expireMinutes) { this.expireMinutes = expireMinutes; }

    public String getDebugCode() { return debugCode; }
    public void setDebugCode(String debugCode) { this.debugCode = debugCode; }
}
