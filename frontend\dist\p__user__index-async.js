((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__user__index'],
{ "src/pages/user/components/UserProfileContent.tsx": function (module, exports, __mako_require__){
/**
 * 用户资料内容组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const UserProfileContent = ()=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [saving, setSaving] = (0, _react.useState)(false);
    const [editing, setEditing] = (0, _react.useState)(false);
    const [userProfile, setUserProfile] = (0, _react.useState)(null);
    const [form] = _antd.Form.useForm();
    (0, _react.useEffect)(()=>{
        fetchUserProfile();
    }, []);
    const fetchUserProfile = async ()=>{
        try {
            setLoading(true);
            const profile = await _services.UserService.getUserProfile();
            setUserProfile(profile);
            form.setFieldsValue({
                name: profile.name,
                email: profile.email
            });
        } catch (error) {
            console.error('获取用户资料失败:', error);
            _antd.message.error('获取用户资料失败');
        } finally{
            setLoading(false);
        }
    };
    const handleSaveProfile = async (values)=>{
        try {
            setSaving(true);
            const updateData = {
                name: values.name
            };
            const updatedProfile = await _services.UserService.updateUserProfile(updateData);
            setUserProfile(updatedProfile);
            setEditing(false);
            _antd.message.success('个人资料更新成功');
        } catch (error) {
            console.error('更新个人资料失败:', error);
            _antd.message.error('更新个人资料失败');
        } finally{
            setSaving(false);
        }
    };
    const handleCancel = ()=>{
        setEditing(false);
        if (userProfile) form.setFieldsValue({
            name: userProfile.name,
            email: userProfile.email
        });
    };
    if (loading || !userProfile) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: "加载中..."
    }, void 0, false, {
        fileName: "src/pages/user/components/UserProfileContent.tsx",
        lineNumber: 91,
        columnNumber: 12
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                display: 'flex',
                alignItems: 'flex-start',
                gap: 24
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        textAlign: 'center'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: 120,
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 99,
                                columnNumber: 36
                            }, void 0)
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 99,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginTop: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Upload, {
                                showUploadList: false,
                                beforeUpload: ()=>{
                                    _antd.message.info('头像上传功能暂未实现');
                                    return false;
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UploadOutlined, {}, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 108,
                                        columnNumber: 29
                                    }, void 0),
                                    size: "small",
                                    children: "更换头像"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 108,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                lineNumber: 101,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 100,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                    lineNumber: 98,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        flex: 1
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                    level: 4,
                                    style: {
                                        margin: 0
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 126,
                                            columnNumber: 15
                                        }, this),
                                        " 基本信息"
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 125,
                                    columnNumber: 13
                                }, this),
                                !editing && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 131,
                                        columnNumber: 23
                                    }, void 0),
                                    onClick: ()=>setEditing(true),
                                    children: "编辑资料"
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 129,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 117,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                            form: form,
                            layout: "vertical",
                            onFinish: handleSaveProfile,
                            disabled: !editing,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: "用户名",
                                    name: "name",
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入用户名'
                                        },
                                        {
                                            max: 100,
                                            message: '用户名不能超过100个字符'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 153,
                                            columnNumber: 30
                                        }, void 0),
                                        placeholder: "请输入用户名"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 153,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 145,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: "邮箱地址",
                                    name: "email",
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 158,
                                            columnNumber: 25
                                        }, void 0),
                                        disabled: true,
                                        placeholder: "邮箱地址不可修改"
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 157,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 156,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        children: editing ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    htmlType: "submit",
                                                    loading: saving,
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SaveOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                        lineNumber: 172,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    children: "保存修改"
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                    lineNumber: 168,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    onClick: handleCancel,
                                                    children: "取消"
                                                }, void 0, false, {
                                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                    lineNumber: 176,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                fileName: "src/pages/user/components/UserProfileContent.tsx",
                                                lineNumber: 181,
                                                columnNumber: 27
                                            }, void 0),
                                            onClick: ()=>setEditing(true),
                                            children: "编辑资料"
                                        }, void 0, false, {
                                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                                            lineNumber: 179,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/user/components/UserProfileContent.tsx",
                                        lineNumber: 165,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                                    lineNumber: 164,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/user/components/UserProfileContent.tsx",
                            lineNumber: 139,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/user/components/UserProfileContent.tsx",
                    lineNumber: 116,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/user/components/UserProfileContent.tsx",
            lineNumber: 96,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/user/components/UserProfileContent.tsx",
        lineNumber: 95,
        columnNumber: 5
    }, this);
};
_s(UserProfileContent, "CVME3i9AOLUbymU4wh4+JIXSLeA=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = UserProfileContent;
var _default = UserProfileContent;
var _c;
$RefreshReg$(_c, "UserProfileContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/user/index.tsx": function (module, exports, __mako_require__){
/**
 * 用户管理页面 - 个人资料管理
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _UserProfileContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/user/components/UserProfileContent.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const { Title } = _antd.Typography;
const UserManagePage = ()=>{
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "用户管理",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UserProfileContent.default, {}, void 0, false, {
                fileName: "src/pages/user/index.tsx",
                lineNumber: 18,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "src/pages/user/index.tsx",
            lineNumber: 17,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/user/index.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
};
_c = UserManagePage;
var _default = UserManagePage;
var _c;
$RefreshReg$(_c, "UserManagePage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__user__index-async.js.map