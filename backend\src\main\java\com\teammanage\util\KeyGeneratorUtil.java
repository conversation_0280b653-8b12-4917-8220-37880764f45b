package com.teammanage.util;

import com.teammanage.service.TokenCryptoService;

/**
 * 密钥生成工具类
 * 用于生成AES和HMAC密钥
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class KeyGeneratorUtil {

    public static void main(String[] args) {
        try {
            System.out.println("=== 生成安全密钥 ===");
            
            String aesKey = TokenCryptoService.generateAESKey();
            System.out.println("AES-256 加密密钥:");
            System.out.println(aesKey);
            System.out.println();
            
            String hmacKey = TokenCryptoService.generateHMACKey();
            System.out.println("HMAC-SHA256 签名密钥:");
            System.out.println(hmacKey);
            System.out.println();
            
            System.out.println("=== 配置示例 ===");
            System.out.println("在 application.yml 中配置:");
            System.out.println("app:");
            System.out.println("  security:");
            System.out.println("    token:");
            System.out.println("      encryption-key: " + aesKey);
            System.out.println("      hmac-key: " + hmacKey);
            System.out.println();
            
            System.out.println("=== 环境变量配置 ===");
            System.out.println("生产环境建议使用环境变量:");
            System.out.println("TOKEN_ENCRYPTION_KEY=" + aesKey);
            System.out.println("TOKEN_HMAC_KEY=" + hmacKey);
            
        } catch (Exception e) {
            System.err.println("生成密钥失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
