server:
  port: 8080
  servlet:
    context-path: /api/v1
  # 生产环境服务器配置
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    max-connections: 8192
    accept-count: 100
    connection-timeout: 20000

spring:
  application:
    name: team-manage

  # 生产环境数据库配置
  datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    url: ${DB_URL:*********************************************************************************************************************************}
    username: ${DB_USERNAME:teammanage_user}
    password: ${DB_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      pool-name: TeamManageHikariCP

  # 缓存配置
  cache:
    type: caffeine

  # JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false

  # 邮件配置（如果需要）
  mail:
    host: ${MAIL_HOST:smtp.example.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          timeout: 25000
          connection-timeout: 25000
          write-timeout: 25000

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    # 生产环境关闭SQL日志
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    db-config:
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
  mapper-locations: classpath*:/mapper/**/*.xml

# JWT配置
jwt:
  secret: ${JWT_SECRET}
  token-expiration: ${JWT_EXPIRATION:86400}  # 24小时 (秒)

# 生产环境日志配置
logging:
  level:
    com.teammanage: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.springframework.boot: INFO
    org.hibernate: WARN
    com.zaxxer.hikari: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:/var/log/teammanage/application.log}
    max-size: 100MB
    max-history: 30
    total-size-cap: 1GB

# Swagger配置（生产环境可选择关闭）
springdoc:
  api-docs:
    enabled: ${SWAGGER_ENABLED:false}
  swagger-ui:
    enabled: ${SWAGGER_ENABLED:false}

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
  health:
    diskspace:
      enabled: true
      threshold: 1GB
    db:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true

# 应用自定义配置
app:
  # 会话管理配置
  session:
    max-concurrent-sessions: ${MAX_CONCURRENT_SESSIONS:10}
    cleanup-interval: 3600

  # 安全配置
  security:
    password-min-length: 8
    max-login-attempts: 5
    lockout-duration: 1800

  # 团队配置
  team:
    max-members: ${MAX_TEAM_MEMBERS:100}
    name-max-length: 100

  # 邀请配置
  invitation:
    expire-hours: ${INVITATION_EXPIRE_HOURS:72}
    base-url: ${INVITATION_BASE_URL:https://app.yourdomain.com}

  # 验证码配置
  verification:
    code-length: 6
    expire-minutes: 5
    max-attempts: 3
    resend-interval: 60

  # CORS配置
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:https://yourdomain.com,https://app.yourdomain.com}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600

# 性能配置
server:
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# 安全配置
security:
  require-ssl: true
  headers:
    frame-options: DENY
    content-type-options: nosniff
    xss-protection: 1; mode=block
    referrer-policy: strict-origin-when-cross-origin
