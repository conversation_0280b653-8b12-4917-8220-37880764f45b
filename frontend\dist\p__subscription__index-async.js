((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__subscription__index'],
{ "src/pages/subscription/components/UnifiedSubscriptionContent.tsx": function (module, exports, __mako_require__){
/**
 * 统一订阅管理内容组件
 * 整合订阅详情和套餐选择功能
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var _api = __mako_require__("src/types/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
const UnifiedSubscriptionContent = ({ currentSubscription, loading, onRefresh })=>{
    _s();
    // 订阅详情相关状态
    const [subscriptionHistory, setSubscriptionHistory] = (0, _react.useState)([]);
    const [usageInfo, setUsageInfo] = (0, _react.useState)(null);
    const [historyModalVisible, setHistoryModalVisible] = (0, _react.useState)(false);
    // 套餐选择相关状态
    const [plans, setPlans] = (0, _react.useState)([]);
    const [plansLoading, setPlansLoading] = (0, _react.useState)(true);
    const [subscribing, setSubscribing] = (0, _react.useState)(false);
    const [selectedPlan, setSelectedPlan] = (0, _react.useState)(null);
    const [subscribeModalVisible, setSubscribeModalVisible] = (0, _react.useState)(false);
    const [duration, setDuration] = (0, _react.useState)(1);
    (0, _react.useEffect)(()=>{
        fetchPlans();
        if (currentSubscription) {
            fetchSubscriptionHistory();
            fetchUsageInfo();
        }
    }, [
        currentSubscription
    ]);
    // 获取套餐列表
    const fetchPlans = async ()=>{
        try {
            setPlansLoading(true);
            const plansData = await _services.SubscriptionService.getActivePlans();
            setPlans(plansData);
        } catch (error) {
            console.error('获取套餐列表失败:', error);
            _antd.message.error('获取套餐列表失败');
        } finally{
            setPlansLoading(false);
        }
    };
    // 获取订阅历史
    const fetchSubscriptionHistory = async ()=>{
        try {
            const history = await _services.SubscriptionService.getSubscriptionHistory();
            setSubscriptionHistory(history);
        } catch (error) {
            console.error('获取订阅历史失败:', error);
        }
    };
    // 获取使用情况
    const fetchUsageInfo = async ()=>{
        try {
            const usage = await _services.SubscriptionService.getUsageInfo();
            setUsageInfo(usage);
        } catch (error) {
            console.error('获取使用情况失败:', error);
        }
    };
    // 处理订阅
    const handleSubscribe = async ()=>{
        if (!selectedPlan) return;
        try {
            setSubscribing(true);
            const request = {
                planId: selectedPlan.id,
                duration: duration
            };
            await _services.SubscriptionService.createSubscription(request);
            _antd.message.success('订阅成功！');
            setSubscribeModalVisible(false);
            onRefresh();
        } catch (error) {
            console.error('订阅失败:', error);
            _antd.message.error('订阅失败，请稍后重试');
        } finally{
            setSubscribing(false);
        }
    };
    // 取消订阅
    const handleCancelSubscription = async ()=>{
        if (!currentSubscription) return;
        _antd.Modal.confirm({
            title: '确认取消订阅',
            content: '取消订阅后，您将失去当前套餐的所有权益。确定要取消吗？',
            okText: '确认取消',
            cancelText: '保留订阅',
            okType: 'danger',
            onOk: async ()=>{
                try {
                    await _services.SubscriptionService.cancelSubscription(currentSubscription.id);
                    _antd.message.success('订阅已取消');
                    onRefresh();
                } catch (error) {
                    console.error('取消订阅失败:', error);
                    _antd.message.error('取消订阅失败');
                }
            }
        });
    };
    // 获取状态标签
    const getStatusTag = (status)=>{
        const statusConfig = {
            [_api.SubscriptionStatus.ACTIVE]: {
                color: 'green',
                text: '有效'
            },
            [_api.SubscriptionStatus.EXPIRED]: {
                color: 'red',
                text: '已过期'
            },
            [_api.SubscriptionStatus.CANCELED]: {
                color: 'default',
                text: '已取消'
            },
            [_api.SubscriptionStatus.PENDING]: {
                color: 'orange',
                text: '待激活'
            }
        };
        const config = statusConfig[status] || {
            color: 'default',
            text: '未知'
        };
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: config.color,
            children: config.text
        }, void 0, false, {
            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
            lineNumber: 172,
            columnNumber: 12
        }, this);
    };
    // 获取套餐推荐标签
    const getPlanRecommendation = (plan)=>{
        if (plan.name === '标准版') return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: "orange",
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.StarOutlined, {}, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 179,
                columnNumber: 35
            }, void 0),
            children: "推荐"
        }, void 0, false, {
            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
            lineNumber: 179,
            columnNumber: 9
        }, this);
        if (plan.name === '企业版') return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
            color: "gold",
            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 186,
                columnNumber: 33
            }, void 0),
            children: "热门"
        }, void 0, false, {
            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
            lineNumber: 186,
            columnNumber: 9
        }, this);
        return null;
    };
    // 套餐特性列表
    const getPlanFeatures = (plan)=>{
        const features = [
            `可创建 ${plan.maxSize === 999999 ? '无限' : plan.maxSize} 个团队`,
            '团队成员无限制',
            '数据安全保障',
            '7x24小时技术支持'
        ];
        if (plan.name !== '免费版') features.push('优先客服支持');
        if (plan.name === '企业版') {
            features.push('定制化服务');
            features.push('专属客户经理');
        }
        return features;
    };
    // 历史记录表格列定义
    const historyColumns = [
        {
            title: '套餐名称',
            dataIndex: 'planName',
            key: 'planName'
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (status)=>getStatusTag(status)
        },
        {
            title: '开始时间',
            dataIndex: 'startDate',
            key: 'startDate',
            render: (date)=>new Date(date).toLocaleDateString()
        },
        {
            title: '结束时间',
            dataIndex: 'endDate',
            key: 'endDate',
            render: (date)=>date ? new Date(date).toLocaleDateString() : '永久'
        },
        {
            title: '价格',
            dataIndex: 'price',
            key: 'price',
            render: (price)=>`¥${price.toFixed(2)}`
        }
    ];
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 254,
                            columnNumber: 13
                        }, void 0),
                        "当前订阅状态"
                    ]
                }, void 0, true, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 253,
                    columnNumber: 11
                }, void 0),
                extra: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ReloadOutlined, {}, void 0, false, {
                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                        lineNumber: 260,
                        columnNumber: 19
                    }, void 0),
                    onClick: onRefresh,
                    loading: loading,
                    children: "刷新"
                }, void 0, false, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 259,
                    columnNumber: 11
                }, void 0),
                style: {
                    marginBottom: 24
                },
                children: currentSubscription ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions, {
                            column: 2,
                            bordered: true,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "套餐名称",
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        children: [
                                            currentSubscription.planName,
                                            getStatusTag(currentSubscription.status)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 273,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 272,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "团队限制",
                                    children: currentSubscription.maxSize === 999999 ? '无限制' : `${currentSubscription.maxSize} 个`
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 278,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "开始时间",
                                    children: new Date(currentSubscription.startDate).toLocaleDateString()
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 283,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "结束时间",
                                    children: currentSubscription.endDate ? new Date(currentSubscription.endDate).toLocaleDateString() : '永久有效'
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 286,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "月费",
                                    children: [
                                        "¥",
                                        currentSubscription.price.toFixed(2)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 291,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Descriptions.Item, {
                                    label: "剩余天数",
                                    children: (usageInfo === null || usageInfo === void 0 ? void 0 : usageInfo.remainingDays) !== undefined ? `${usageInfo.remainingDays} 天` : '计算中...'
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 294,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 271,
                            columnNumber: 13
                        }, this),
                        usageInfo && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginTop: 16
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "团队使用情况："
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 303,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                    percent: usageInfo.usagePercentage,
                                    format: ()=>`${usageInfo.currentUsage}/${usageInfo.maxUsage === 999999 ? '∞' : usageInfo.maxUsage}`,
                                    style: {
                                        marginTop: 8
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 304,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 302,
                            columnNumber: 15
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginTop: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UpOutlined, {}, void 0, false, {
                                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                            lineNumber: 318,
                                            columnNumber: 25
                                        }, void 0),
                                        onClick: ()=>setSubscribeModalVisible(true),
                                        children: "升级套餐"
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 316,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.HistoryOutlined, {}, void 0, false, {
                                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                            lineNumber: 324,
                                            columnNumber: 25
                                        }, void 0),
                                        onClick: ()=>setHistoryModalVisible(true),
                                        children: "查看历史"
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 323,
                                        columnNumber: 17
                                    }, this),
                                    currentSubscription.status === _api.SubscriptionStatus.ACTIVE && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.StopOutlined, {}, void 0, false, {
                                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                            lineNumber: 332,
                                            columnNumber: 27
                                        }, void 0),
                                        onClick: handleCancelSubscription,
                                        children: "取消订阅"
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 330,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                lineNumber: 315,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 314,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 270,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Empty, {
                    description: "暂无有效订阅",
                    image: _antd.Empty.PRESENTED_IMAGE_SIMPLE,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ShoppingCartOutlined, {}, void 0, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 348,
                            columnNumber: 21
                        }, void 0),
                        onClick: ()=>setSubscribeModalVisible(true),
                        children: "立即订阅"
                    }, void 0, false, {
                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                        lineNumber: 346,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 342,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 251,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ShoppingCartOutlined, {}, void 0, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 361,
                            columnNumber: 13
                        }, void 0),
                        "选择套餐"
                    ]
                }, void 0, true, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 360,
                    columnNumber: 11
                }, void 0),
                loading: plansLoading,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                    gutter: [
                        16,
                        16
                    ],
                    children: plans.map((plan)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                            xs: 24,
                            sm: 12,
                            lg: 6,
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                hoverable: true,
                                className: `plan-card ${(currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? 'current-plan' : ''}`,
                                actions: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? 'default' : 'primary',
                                        disabled: (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id,
                                        onClick: ()=>{
                                            setSelectedPlan(plan);
                                            setSubscribeModalVisible(true);
                                        },
                                        children: (currentSubscription === null || currentSubscription === void 0 ? void 0 : currentSubscription.planId) === plan.id ? '当前套餐' : '选择此套餐'
                                    }, "subscribe", false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 374,
                                        columnNumber: 19
                                    }, void 0)
                                ],
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        style: {
                                            textAlign: 'center'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                level: 4,
                                                children: [
                                                    plan.name,
                                                    getPlanRecommendation(plan)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                lineNumber: 394,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                style: {
                                                    fontSize: 32,
                                                    fontWeight: 'bold',
                                                    color: '#1890ff'
                                                },
                                                children: [
                                                    "¥",
                                                    plan.price.toFixed(0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                        style: {
                                                            fontSize: 14,
                                                            color: '#666'
                                                        },
                                                        children: "/月"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                        lineNumber: 406,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                lineNumber: 398,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                type: "secondary",
                                                children: plan.description
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                lineNumber: 408,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 393,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 411,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List, {
                                        size: "small",
                                        dataSource: getPlanFeatures(plan),
                                        renderItem: (feature)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.List.Item, {
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                            style: {
                                                                color: '#52c41a'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                            lineNumber: 419,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        feature
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                    lineNumber: 418,
                                                    columnNumber: 23
                                                }, void 0)
                                            }, void 0, false, {
                                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                                lineNumber: 417,
                                                columnNumber: 21
                                            }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                        lineNumber: 413,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                lineNumber: 370,
                                columnNumber: 15
                            }, this)
                        }, plan.id, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 369,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 367,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 358,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "确认订阅",
                open: subscribeModalVisible,
                onOk: handleSubscribe,
                onCancel: ()=>setSubscribeModalVisible(false),
                confirmLoading: subscribing,
                okText: "确认订阅",
                cancelText: "取消",
                children: selectedPlan && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: `您选择了 ${selectedPlan.name}`,
                            description: selectedPlan.description,
                            type: "info",
                            style: {
                                marginBottom: 16
                            }
                        }, void 0, false, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 443,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "订阅时长："
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 451,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.InputNumber, {
                                    min: 1,
                                    max: 12,
                                    value: duration,
                                    onChange: (value)=>setDuration(value || 1),
                                    addonAfter: "个月",
                                    style: {
                                        marginLeft: 8
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 452,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 450,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    strong: true,
                                    children: "总费用："
                                }, void 0, false, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 463,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    style: {
                                        fontSize: 18,
                                        color: '#1890ff',
                                        marginLeft: 8
                                    },
                                    children: [
                                        "¥",
                                        (selectedPlan.price * duration).toFixed(2)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                                    lineNumber: 464,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                            lineNumber: 462,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 442,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 432,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "订阅历史",
                open: historyModalVisible,
                onCancel: ()=>setHistoryModalVisible(false),
                footer: null,
                width: 800,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Table, {
                    columns: historyColumns,
                    dataSource: subscriptionHistory,
                    rowKey: "id",
                    pagination: {
                        pageSize: 10
                    }
                }, void 0, false, {
                    fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                    lineNumber: 480,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
                lineNumber: 473,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/subscription/components/UnifiedSubscriptionContent.tsx",
        lineNumber: 249,
        columnNumber: 5
    }, this);
};
_s(UnifiedSubscriptionContent, "AqX/RzCYRxfO7hNh5ekvwTxLd0E=");
_c = UnifiedSubscriptionContent;
var _default = UnifiedSubscriptionContent;
var _c;
$RefreshReg$(_c, "UnifiedSubscriptionContent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/subscription/index.tsx": function (module, exports, __mako_require__){
/**
 * 订阅管理页面 - 统一的订阅管理界面
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var _UnifiedSubscriptionContent = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/subscription/components/UnifiedSubscriptionContent.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const SubscriptionPage = ()=>{
    _s();
    const [currentSubscription, setCurrentSubscription] = (0, _react.useState)(null);
    const [loading, setLoading] = (0, _react.useState)(true);
    (0, _react.useEffect)(()=>{
        fetchCurrentSubscription();
    }, []);
    const fetchCurrentSubscription = async ()=>{
        try {
            setLoading(true);
            const subscription = await _services.SubscriptionService.getCurrentSubscription();
            setCurrentSubscription(subscription);
        } catch (error) {
            console.error('获取当前订阅失败:', error);
            _antd.message.error('获取订阅信息失败');
        } finally{
            setLoading(false);
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "订阅管理",
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UnifiedSubscriptionContent.default, {
            currentSubscription: currentSubscription,
            loading: loading,
            onRefresh: fetchCurrentSubscription
        }, void 0, false, {
            fileName: "src/pages/subscription/index.tsx",
            lineNumber: 38,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/subscription/index.tsx",
        lineNumber: 37,
        columnNumber: 5
    }, this);
};
_s(SubscriptionPage, "XLrtBtOa7IP1iypwWvRQWDiKewU=");
_c = SubscriptionPage;
var _default = SubscriptionPage;
var _c;
$RefreshReg$(_c, "SubscriptionPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__subscription__index-async.js.map