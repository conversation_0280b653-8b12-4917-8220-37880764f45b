{"version": 3, "sources": ["src/pages/404.tsx"], "sourcesContent": ["import { history } from '@umijs/max';\nimport { <PERSON><PERSON>, Card, Result } from 'antd';\nimport React from 'react';\n\nconst NoFoundPage: React.FC = () => (\n  <Card variant=\"borderless\">\n    <Result\n      status=\"404\"\n      title=\"404\"\n      subTitle=\"抱歉，您访问的页面不存在。\"\n      extra={\n        <Button type=\"primary\" onClick={() => history.push('/')}>\n          返回首页\n        </Button>\n      }\n    />\n  </Card>\n);\n\nexport default NoFoundPage;\n"], "names": [], "mappings": ";;;;;;;4BAmBA;;;eAAA;;;;;;;4BAnBwB;6BACa;uEACnB;;;;;;;;;AAElB,MAAM,cAAwB,kBAC5B,2BAAC,UAAI;QAAC,SAAQ;kBACZ,cAAA,2BAAC,YAAM;YACL,QAAO;YACP,OAAM;YACN,UAAS;YACT,qBACE,2BAAC,YAAM;gBAAC,MAAK;gBAAU,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;0BAAM;;;;;;;;;;;;;;;;KAP3D;IAeN,WAAe"}