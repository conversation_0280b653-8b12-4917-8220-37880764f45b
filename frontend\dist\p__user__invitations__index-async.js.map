{"version": 3, "sources": ["src/pages/user/invitations/index.tsx"], "sourcesContent": ["/**\n * 用户邀请页面\n * \n * 功能特性：\n * - 显示用户收到的所有邀请\n * - 支持接受或拒绝邀请\n * - 显示邀请详情和团队信息\n * - 自动刷新待处理邀请\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  message,\n  Modal,\n  Form,\n  Input,\n  Typography,\n  Tag,\n  Tooltip,\n  Avatar,\n} from 'antd';\nimport {\n  CheckOutlined,\n  CloseOutlined,\n  TeamOutlined,\n  UserOutlined,\n  ReloadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\n\n// 导入服务和类型\nimport { InvitationService } from '@/services';\nimport type { TeamInvitationResponse, RespondInvitationRequest } from '@/types/api';\nimport { InvitationStatus } from '@/types/api';\nimport InvitationStatusComponent from '@/components/InvitationStatus';\n\nconst { Text, Title } = Typography;\nconst { TextArea } = Input;\n\nconst UserInvitationsPage: React.FC = () => {\n  const [invitations, setInvitations] = useState<TeamInvitationResponse[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [respondModalVisible, setRespondModalVisible] = useState(false);\n  const [selectedInvitation, setSelectedInvitation] = useState<TeamInvitationResponse | null>(null);\n  const [respondForm] = Form.useForm();\n\n  // 获取邀请列表\n  const fetchInvitations = async () => {\n    try {\n      setLoading(true);\n      const invitationList = await InvitationService.getUserReceivedInvitations();\n      setInvitations(invitationList || []);\n    } catch (error) {\n      console.error('获取邀请列表失败:', error);\n      message.error('获取邀请列表失败');\n      setInvitations([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchInvitations();\n  }, []);\n\n  // 响应邀请\n  const handleRespondInvitation = async (invitation: TeamInvitationResponse, accept: boolean) => {\n    setSelectedInvitation(invitation);\n    respondForm.setFieldsValue({ accept });\n    setRespondModalVisible(true);\n  };\n\n  // 提交响应\n  const handleSubmitResponse = async (values: { accept: boolean; message?: string }) => {\n    if (!selectedInvitation) return;\n\n    try {\n      const request: RespondInvitationRequest = {\n        accept: values.accept,\n        message: values.message,\n      };\n\n      await InvitationService.respondToInvitation(selectedInvitation.id, request);\n      \n      const action = values.accept ? '接受' : '拒绝';\n      message.success(`邀请${action}成功`);\n      \n      setRespondModalVisible(false);\n      respondForm.resetFields();\n      setSelectedInvitation(null);\n      fetchInvitations();\n    } catch (error) {\n      console.error('响应邀请失败:', error);\n      message.error('响应邀请失败');\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<TeamInvitationResponse> = [\n    {\n      title: '团队信息',\n      key: 'team',\n      render: (_, record) => (\n        <Space>\n          <Avatar icon={<TeamOutlined />} />\n          <div>\n            <Text strong>{record.teamName}</Text>\n            <br />\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n              团队ID: {record.teamId}\n            </Text>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '邀请人',\n      key: 'inviter',\n      render: (_, record) => (\n        <Space>\n          <Avatar icon={<UserOutlined />} />\n          <div>\n            <Text strong>{record.inviterName}</Text>\n            <br />\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n              {record.inviterEmail}\n            </Text>\n          </div>\n        </Space>\n      ),\n    },\n    {\n      title: '邀请状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status, record) => (\n        <InvitationStatusComponent \n          status={status} \n          isExpired={record.isExpired} \n        />\n      ),\n    },\n    {\n      title: '邀请时间',\n      dataIndex: 'invitedAt',\n      key: 'invitedAt',\n      render: (time) => (\n        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n          {dayjs(time).format('MM-DD HH:mm')}\n        </Tooltip>\n      ),\n      sorter: (a, b) => dayjs(a.invitedAt).unix() - dayjs(b.invitedAt).unix(),\n      defaultSortOrder: 'descend',\n    },\n    {\n      title: '过期时间',\n      dataIndex: 'expiresAt',\n      key: 'expiresAt',\n      render: (time, record) => {\n        const isExpired = record.isExpired;\n        return (\n          <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>\n            <Text type={isExpired ? 'danger' : 'secondary'}>\n              {dayjs(time).format('MM-DD HH:mm')}\n            </Text>\n          </Tooltip>\n        );\n      },\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          {record.canBeResponded && (\n            <>\n              <Button\n                type=\"primary\"\n                size=\"small\"\n                icon={<CheckOutlined />}\n                onClick={() => handleRespondInvitation(record, true)}\n              >\n                接受\n              </Button>\n              <Button\n                size=\"small\"\n                icon={<CloseOutlined />}\n                onClick={() => handleRespondInvitation(record, false)}\n              >\n                拒绝\n              </Button>\n            </>\n          )}\n        </Space>\n      ),\n    },\n  ];\n\n  // 统计信息\n  const pendingCount = invitations.filter(inv => inv.status === InvitationStatus.PENDING && !inv.isExpired).length;\n  const acceptedCount = invitations.filter(inv => inv.status === InvitationStatus.ACCEPTED).length;\n  const rejectedCount = invitations.filter(inv => inv.status === InvitationStatus.REJECTED).length;\n\n  return (\n    <PageContainer\n      title=\"我的邀请\"\n      subTitle=\"管理您收到的团队邀请\"\n      extra={[\n        <Button \n          key=\"refresh\"\n          icon={<ReloadOutlined />} \n          onClick={fetchInvitations}\n          loading={loading}\n        >\n          刷新\n        </Button>\n      ]}\n    >\n      {/* 统计卡片 */}\n      <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n        <Card>\n          <Space size=\"large\">\n            <div>\n              <Text type=\"secondary\">待处理邀请</Text>\n              <br />\n              <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>\n                {pendingCount}\n              </Text>\n            </div>\n            <div>\n              <Text type=\"secondary\">已接受</Text>\n              <br />\n              <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>\n                {acceptedCount}\n              </Text>\n            </div>\n            <div>\n              <Text type=\"secondary\">已拒绝</Text>\n              <br />\n              <Text style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>\n                {rejectedCount}\n              </Text>\n            </div>\n          </Space>\n        </Card>\n\n        {/* 邀请列表 */}\n        <Card title=\"邀请列表\">\n          <Table\n            columns={columns}\n            dataSource={invitations}\n            rowKey=\"id\"\n            loading={loading}\n            pagination={{\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: (total) => `共 ${total} 条邀请记录`,\n              pageSize: 10,\n            }}\n          />\n        </Card>\n      </Space>\n\n      {/* 响应邀请弹窗 */}\n      <Modal\n        title={`${respondForm.getFieldValue('accept') ? '接受' : '拒绝'}邀请`}\n        open={respondModalVisible}\n        onCancel={() => {\n          setRespondModalVisible(false);\n          respondForm.resetFields();\n          setSelectedInvitation(null);\n        }}\n        footer={null}\n        width={500}\n      >\n        {selectedInvitation && (\n          <div style={{ marginBottom: 16 }}>\n            <Text strong>团队：</Text> {selectedInvitation.teamName}\n            <br />\n            <Text strong>邀请人：</Text> {selectedInvitation.inviterName}\n            <br />\n            {selectedInvitation.message && (\n              <>\n                <Text strong>邀请消息：</Text>\n                <div style={{ \n                  background: '#f5f5f5', \n                  padding: 8, \n                  borderRadius: 4, \n                  marginTop: 4 \n                }}>\n                  {selectedInvitation.message}\n                </div>\n              </>\n            )}\n          </div>\n        )}\n        \n        <Form\n          form={respondForm}\n          layout=\"vertical\"\n          onFinish={handleSubmitResponse}\n        >\n          <Form.Item name=\"accept\" hidden>\n            <Input />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"message\"\n            label=\"回复消息（可选）\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"您可以添加一些回复消息...\"\n              maxLength={200}\n            />\n          </Form.Item>\n          \n          <Form.Item>\n            <Space>\n              <Button \n                type=\"primary\" \n                htmlType=\"submit\"\n                icon={respondForm.getFieldValue('accept') ? <CheckOutlined /> : <CloseOutlined />}\n              >\n                确认{respondForm.getFieldValue('accept') ? '接受' : '拒绝'}\n              </Button>\n              <Button onClick={() => setRespondModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </PageContainer>\n  );\n};\n\nexport default UserInvitationsPage;\n"], "names": [], "mappings": ";;;AAAA;;;;;;;;CAQC;;;;4BA+UD;;;eAAA;;;;;;;wEA7U2C;sCACb;6BAcvB;8BAOA;uEAEW;iCAGgB;4BAED;kFACK;;;;;;;;;;AAEtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAE1B,MAAM,sBAAgC;;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAA2B,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,IAAA,eAAQ,EAAC;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAgC;IAC5F,MAAM,CAAC,YAAY,GAAG,UAAI,CAAC,OAAO;IAElC,SAAS;IACT,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,MAAM,iBAAiB,MAAM,2BAAiB,CAAC,0BAA0B;YACzE,eAAe,kBAAkB,EAAE;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;YACd,eAAe,EAAE;QACnB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,0BAA0B,OAAO,YAAoC;QACzE,sBAAsB;QACtB,YAAY,cAAc,CAAC;YAAE;QAAO;QACpC,uBAAuB;IACzB;IAEA,OAAO;IACP,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,oBAAoB;QAEzB,IAAI;YACF,MAAM,UAAoC;gBACxC,QAAQ,OAAO,MAAM;gBACrB,SAAS,OAAO,OAAO;YACzB;YAEA,MAAM,2BAAiB,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,EAAE;YAEnE,MAAM,SAAS,OAAO,MAAM,GAAG,OAAO;YACtC,aAAO,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC;YAE/B,uBAAuB;YACvB,YAAY,WAAW;YACvB,sBAAsB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,QAAQ;IACR,MAAM,UAA+C;QACnD;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BAAC,oBAAM,2BAAC,mBAAY;;;;;;;;;;sCAC3B,2BAAC;;8CACC,2BAAC;oCAAK,MAAM;8CAAE,OAAO,QAAQ;;;;;;8CAC7B,2BAAC;;;;;8CACD,2BAAC;oCAAK,MAAK;oCAAY,OAAO;wCAAE,UAAU;oCAAO;;wCAAG;wCAC3C,OAAO,MAAM;;;;;;;;;;;;;;;;;;;QAK9B;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;;sCACJ,2BAAC,YAAM;4BAAC,oBAAM,2BAAC,mBAAY;;;;;;;;;;sCAC3B,2BAAC;;8CACC,2BAAC;oCAAK,MAAM;8CAAE,OAAO,WAAW;;;;;;8CAChC,2BAAC;;;;;8CACD,2BAAC;oCAAK,MAAK;oCAAY,OAAO;wCAAE,UAAU;oCAAO;8CAC9C,OAAO,YAAY;;;;;;;;;;;;;;;;;;QAK9B;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,QAAQ,uBACf,2BAAC,yBAAyB;oBACxB,QAAQ;oBACR,WAAW,OAAO,SAAS;;;;;;QAGjC;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,qBACP,2BAAC,aAAO;oBAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;8BAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;;YAGxB,QAAQ,CAAC,GAAG,IAAM,IAAA,cAAK,EAAC,EAAE,SAAS,EAAE,IAAI,KAAK,IAAA,cAAK,EAAC,EAAE,SAAS,EAAE,IAAI;YACrE,kBAAkB;QACpB;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAM;gBACb,MAAM,YAAY,OAAO,SAAS;gBAClC,qBACE,2BAAC,aAAO;oBAAC,OAAO,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;8BACjC,cAAA,2BAAC;wBAAK,MAAM,YAAY,WAAW;kCAChC,IAAA,cAAK,EAAC,MAAM,MAAM,CAAC;;;;;;;;;;;YAI5B;QACF;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,2BAAC,WAAK;8BACH,OAAO,cAAc,kBACpB;;0CACE,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAK;gCACL,oBAAM,2BAAC,oBAAa;;;;;gCACpB,SAAS,IAAM,wBAAwB,QAAQ;0CAChD;;;;;;0CAGD,2BAAC,YAAM;gCACL,MAAK;gCACL,oBAAM,2BAAC,oBAAa;;;;;gCACpB,SAAS,IAAM,wBAAwB,QAAQ;0CAChD;;;;;;;;;;;;;QAOX;KACD;IAED,OAAO;IACP,MAAM,eAAe,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,qBAAgB,CAAC,OAAO,IAAI,CAAC,IAAI,SAAS,EAAE,MAAM;IAChH,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,qBAAgB,CAAC,QAAQ,EAAE,MAAM;IAChG,MAAM,gBAAgB,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,qBAAgB,CAAC,QAAQ,EAAE,MAAM;IAEhG,qBACE,2BAAC,4BAAa;QACZ,OAAM;QACN,UAAS;QACT,OAAO;0BACL,2BAAC,YAAM;gBAEL,oBAAM,2BAAC,qBAAc;;;;;gBACrB,SAAS;gBACT,SAAS;0BACV;eAJK;;;;;SAOP;;0BAGD,2BAAC,WAAK;gBAAC,WAAU;gBAAW,MAAK;gBAAQ,OAAO;oBAAE,OAAO;gBAAO;;kCAC9D,2BAAC,UAAI;kCACH,cAAA,2BAAC,WAAK;4BAAC,MAAK;;8CACV,2BAAC;;sDACC,2BAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,2BAAC;;;;;sDACD,2BAAC;4CAAK,OAAO;gDAAE,UAAU;gDAAQ,YAAY;gDAAQ,OAAO;4CAAU;sDACnE;;;;;;;;;;;;8CAGL,2BAAC;;sDACC,2BAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,2BAAC;;;;;sDACD,2BAAC;4CAAK,OAAO;gDAAE,UAAU;gDAAQ,YAAY;gDAAQ,OAAO;4CAAU;sDACnE;;;;;;;;;;;;8CAGL,2BAAC;;sDACC,2BAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,2BAAC;;;;;sDACD,2BAAC;4CAAK,OAAO;gDAAE,UAAU;gDAAQ,YAAY;gDAAQ,OAAO;4CAAU;sDACnE;;;;;;;;;;;;;;;;;;;;;;;kCAOT,2BAAC,UAAI;wBAAC,OAAM;kCACV,cAAA,2BAAC,WAAK;4BACJ,SAAS;4BACT,YAAY;4BACZ,QAAO;4BACP,SAAS;4BACT,YAAY;gCACV,iBAAiB;gCACjB,iBAAiB;gCACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,MAAM,CAAC;gCACxC,UAAU;4BACZ;;;;;;;;;;;;;;;;;0BAMN,2BAAC,WAAK;gBACJ,OAAO,CAAC,EAAE,YAAY,aAAa,CAAC,YAAY,OAAO,KAAK,EAAE,CAAC;gBAC/D,MAAM;gBACN,UAAU;oBACR,uBAAuB;oBACvB,YAAY,WAAW;oBACvB,sBAAsB;gBACxB;gBACA,QAAQ;gBACR,OAAO;;oBAEN,oCACC,2BAAC;wBAAI,OAAO;4BAAE,cAAc;wBAAG;;0CAC7B,2BAAC;gCAAK,MAAM;0CAAC;;;;;;4BAAU;4BAAE,mBAAmB,QAAQ;0CACpD,2BAAC;;;;;0CACD,2BAAC;gCAAK,MAAM;0CAAC;;;;;;4BAAW;4BAAE,mBAAmB,WAAW;0CACxD,2BAAC;;;;;4BACA,mBAAmB,OAAO,kBACzB;;kDACE,2BAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,2BAAC;wCAAI,OAAO;4CACV,YAAY;4CACZ,SAAS;4CACT,cAAc;4CACd,WAAW;wCACb;kDACG,mBAAmB,OAAO;;;;;;;;;;;;;;kCAOrC,2BAAC,UAAI;wBACH,MAAM;wBACN,QAAO;wBACP,UAAU;;0CAEV,2BAAC,UAAI,CAAC,IAAI;gCAAC,MAAK;gCAAS,MAAM;0CAC7B,cAAA,2BAAC,WAAK;;;;;;;;;;0CAGR,2BAAC,UAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAM;0CAEN,cAAA,2BAAC;oCACC,MAAM;oCACN,aAAY;oCACZ,WAAW;;;;;;;;;;;0CAIf,2BAAC,UAAI,CAAC,IAAI;0CACR,cAAA,2BAAC,WAAK;;sDACJ,2BAAC,YAAM;4CACL,MAAK;4CACL,UAAS;4CACT,MAAM,YAAY,aAAa,CAAC,0BAAY,2BAAC,oBAAa;;;;uEAAM,2BAAC,oBAAa;;;;;;gDAC/E;gDACI,YAAY,aAAa,CAAC,YAAY,OAAO;;;;;;;sDAElD,2BAAC,YAAM;4CAAC,SAAS,IAAM,uBAAuB;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;GAxSM;;QAKkB,UAAI,CAAC;;;KALvB;IA0SN,WAAe"}