/**
 * 好友列表组件
 */

import {
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Empty,
  Form,
  Input,
  List,
  Modal,
  message,
  Popconfirm,
  Space,
  Tooltip,
  Typography,
} from 'antd';
import React, { useState } from 'react';
import { FriendService } from '@/services';
import type { FriendWithRemark } from '@/types/api';

const { Text } = Typography;
const { Search } = Input;

interface FriendListProps {
  friends: FriendWithRemark[];
  loading: boolean;
  onRemoveFriend: () => void;
  onRefresh: () => void;
}

const FriendList: React.FC<FriendListProps> = ({
  friends,
  loading,
  onRemoveFriend,
  onRefresh,
}) => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [removing, setRemoving] = useState<number | null>(null);
  const [remarkModalVisible, setRemarkModalVisible] = useState(false);
  const [currentFriend, setCurrentFriend] = useState<FriendWithRemark | null>(
    null,
  );
  const [remarkForm] = Form.useForm();

  /**
   * 根据搜索关键词过滤好友列表
   *
   * 搜索范围：
   * - 好友姓名（不区分大小写）
   * - 好友邮箱（不区分大小写）
   */
  const filteredFriends = friends.filter(
    (friend) =>
      friend.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      friend.email.toLowerCase().includes(searchKeyword.toLowerCase()),
  );

  /**
   * 删除好友的处理函数
   *
   * 执行流程：
   * 1. 设置删除状态，显示加载动画
   * 2. 调用API删除好友关系
   * 3. 调用父组件回调，刷新好友列表
   * 4. 处理错误情况并显示错误消息
   * 5. 清除删除状态
   *
   * @param friend 要删除的好友对象
   */
  const handleRemoveFriend = async (friend: Account) => {
    try {
      setRemoving(friend.id);
      await FriendService.removeFriend(friend.id);
      message.success(`已删除好友 "${friend.name}"`);
      onRemoveFriend();
    } catch (error) {
      console.error('删除好友失败:', error);
      message.error('删除好友失败，请稍后重试');
    } finally {
      setRemoving(null);
    }
  };

  /**
   * 打开备注编辑模态框
   */
  const handleEditRemark = (friend: FriendWithRemark) => {
    setCurrentFriend(friend);
    setRemarkModalVisible(true);
    // 直接使用好友对象中的备注信息
    remarkForm.setFieldsValue({ remark: friend.remark || '' });
  };

  /**
   * 保存好友备注
   */
  const handleSaveRemark = async (values: { remark: string }) => {
    if (!currentFriend) return;

    try {
      await FriendService.setFriendRemark({
        friendId: currentFriend.id,
        remark: values.remark,
      });
      message.success('备注保存成功');
      setRemarkModalVisible(false);
      setCurrentFriend(null);
      remarkForm.resetFields();
      onRefresh();
    } catch (error) {
      console.error('保存备注失败:', error);
      message.error('保存备注失败');
    }
  };

  if (friends.length === 0 && !loading) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="暂无好友"
        style={{ padding: '50px 0' }}
      >
        <Text type="secondary">您还没有添加任何好友，快去添加好友吧！</Text>
      </Empty>
    );
  }

  return (
    <div>
      {/* 搜索框 */}
      <div style={{ marginBottom: 16 }}>
        <Search
          placeholder="搜索好友姓名或邮箱"
          allowClear
          enterButton={<SearchOutlined />}
          size="large"
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          style={{ maxWidth: 400 }}
        />
      </div>

      {/* 好友列表 */}
      <List
        loading={loading}
        dataSource={filteredFriends}
        renderItem={(friend) => (
          <List.Item
            actions={[
              <Tooltip key="remark" title="编辑备注">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => handleEditRemark(friend)}
                  size="small"
                >
                  备注
                </Button>
              </Tooltip>,
              <Popconfirm
                key="delete"
                title="确认删除好友"
                description={`确定要删除好友 "${friend.name}" 吗？`}
                icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
                onConfirm={() => handleRemoveFriend(friend)}
                okText="确认删除"
                cancelText="取消"
                okType="danger"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  loading={removing === friend.id}
                  size="small"
                >
                  删除
                </Button>
              </Popconfirm>,
            ]}
          >
            <List.Item.Meta
              avatar={
                <Avatar
                  size={48}
                  icon={<UserOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                >
                  {friend.name.charAt(0).toUpperCase()}
                </Avatar>
              }
              title={
                <Space>
                  <Text strong>{friend.name}</Text>
                </Space>
              }
              description={
                <Space direction="vertical" size={4}>
                  <Text type="secondary">{friend.email}</Text>
                  {friend.remark && (
                    <Text
                      type="secondary"
                      style={{ fontSize: 12, fontStyle: 'italic' }}
                    >
                      备注: {friend.remark}
                    </Text>
                  )}
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    加入时间: {new Date(friend.createdAt).toLocaleDateString()}
                  </Text>
                </Space>
              }
            />
          </List.Item>
        )}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 个好友`,
        }}
      />

      {filteredFriends.length === 0 && searchKeyword && (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={`没有找到包含 "${searchKeyword}" 的好友`}
          style={{ padding: '50px 0' }}
        />
      )}

      {/* 备注编辑模态框 */}
      <Modal
        title="编辑好友备注"
        open={remarkModalVisible}
        onCancel={() => {
          setRemarkModalVisible(false);
          setCurrentFriend(null);
          remarkForm.resetFields();
        }}
        footer={null}
        width={400}
      >
        <Form form={remarkForm} layout="vertical" onFinish={handleSaveRemark}>
          <Form.Item
            label="好友备注"
            name="remark"
            rules={[{ max: 50, message: '备注长度不能超过50个字符' }]}
          >
            <Input.TextArea
              placeholder="为好友添加备注..."
              rows={3}
              maxLength={50}
              showCount
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setRemarkModalVisible(false);
                  setCurrentFriend(null);
                  remarkForm.resetFields();
                }}
              >
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FriendList;
