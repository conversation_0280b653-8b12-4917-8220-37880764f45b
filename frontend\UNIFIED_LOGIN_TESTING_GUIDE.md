# 统一验证码登录/注册系统测试指南

## 🎯 测试目标

验证统一验证码登录/注册系统的完整功能，确保新用户自动注册和现有用户登录都能正常工作。

## 🔧 测试环境准备

### 后端启动
```bash
cd backend
mvn spring-boot:run
```

### 前端启动
```bash
cd frontend
npm run dev
```

### 验证服务状态
- 后端：http://localhost:8080/api/v1/actuator/health
- 前端：http://localhost:8000

## 📋 测试用例

### 1. 新用户自动注册测试

#### 测试步骤
1. 访问登录页面：http://localhost:8000/user/login
2. 输入一个未注册的邮箱（如：<EMAIL>）
3. 点击"发送验证码"按钮
4. 查看后端控制台日志，找到验证码
5. 在前端输入验证码
6. 点击"登录 / 注册"按钮

#### 预期结果
- ✅ 显示"欢迎加入！账号已自动创建并登录成功！"
- ✅ 成功跳转到个人中心或团队创建页面
- ✅ 后端日志显示"自动创建新用户"
- ✅ 数据库中创建了新用户记录

#### 验证点
```sql
-- 检查数据库中的新用户
SELECT * FROM account WHERE email = '<EMAIL>';
-- 验证字段：
-- - email: <EMAIL>
-- - name: newuser (邮箱前缀)
-- - password_hash: NULL
-- - created_at: 当前时间
```

### 2. 现有用户登录测试

#### 测试步骤
1. 使用已注册的邮箱（如：<EMAIL>）
2. 点击"发送验证码"
3. 从后端日志获取验证码
4. 输入验证码并登录

#### 预期结果
- ✅ 显示"登录成功！"
- ✅ 正常跳转到用户界面
- ✅ 后端日志显示正常登录，isNewUser=false

### 3. 验证码功能测试

#### 3.1 验证码发送测试
```bash
# API测试
curl -X POST http://localhost:8080/api/v1/auth/send-code \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","type":"login"}'
```

#### 预期响应
```json
{
  "success": true,
  "message": "验证码发送成功",
  "data": {
    "success": true,
    "message": "验证码发送成功",
    "expireMinutes": 5
  }
}
```

#### 3.2 验证码验证测试
```bash
# 使用从日志获取的验证码
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","code":"123456"}'
```

#### 预期响应（新用户）
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJ...",
    "expiresIn": 86400000,
    "user": {
      "id": 123,
      "email": "<EMAIL>",
      "name": "test"
    },
    "teams": [],
    "isNewUser": true
  }
}
```

### 4. 错误处理测试

#### 4.1 无效验证码
- 输入错误的验证码
- 预期：显示"验证码错误"

#### 4.2 过期验证码
- 等待5分钟后使用验证码
- 预期：显示"验证码已过期"

#### 4.3 重发限制
- 连续点击"发送验证码"
- 预期：显示倒计时，按钮禁用60秒

#### 4.4 邮箱格式验证
- 输入无效邮箱格式
- 预期：显示"请输入有效的邮箱地址"

### 5. 界面功能测试

#### 5.1 页面元素检查
- ✅ 只显示一个登录表单（无注册标签页）
- ✅ 显示"新用户将自动完成注册并登录"提示
- ✅ 按钮文字为"登录 / 注册"
- ✅ 验证码输入框带发送按钮

#### 5.2 倒计时功能
- ✅ 发送验证码后显示60秒倒计时
- ✅ 倒计时期间按钮禁用
- ✅ 倒计时结束后可重新发送

#### 5.3 开发调试功能
- ✅ 开发环境显示调试面板
- ✅ 控制台输出调试信息
- ✅ `window.devHelper` 对象可用

### 6. 兼容性测试

#### 6.1 邀请链接功能
1. 创建邀请链接
2. 访问邀请链接页面
3. 验证新用户注册功能仍然可用
4. 确认需要姓名和密码的场景正常

#### 6.2 现有功能
- ✅ 团队管理功能正常
- ✅ 用户个人中心正常
- ✅ 其他页面不受影响

## 🔍 调试技巧

### 获取验证码
```bash
# 方法1：查看后端控制台日志
# 格式：发送验证码: email=<EMAIL>, code=123456, type=login

# 方法2：使用开发工具
window.devHelper.quickFillVerificationCode('123456');

# 方法3：查看网络请求
# 开发环境下验证码可能在响应中返回（仅调试用）
```

### 数据库查询
```sql
-- 查看用户表
SELECT id, email, name, password_hash, created_at 
FROM account 
ORDER BY created_at DESC 
LIMIT 10;

-- 查看验证码缓存（如果使用数据库存储）
-- 注意：当前使用内存缓存，无法直接查询
```

### 日志分析
```bash
# 关键日志关键词
grep "自动创建新用户" backend.log
grep "用户登录成功" backend.log
grep "验证码验证成功" backend.log
```

## 📊 性能测试

### 并发登录测试
```bash
# 使用Apache Bench测试
ab -n 100 -c 10 -H "Content-Type: application/json" \
   -p login_data.json \
   http://localhost:8080/api/v1/auth/login
```

### 验证码发送频率测试
- 测试60秒重发限制
- 验证服务器资源使用
- 检查内存缓存性能

## ✅ 验收标准

### 功能验收
- [ ] 新用户自动注册并登录成功
- [ ] 现有用户正常登录
- [ ] 验证码发送和验证正常
- [ ] 错误处理友好准确
- [ ] 界面简洁易用

### 性能验收
- [ ] 登录响应时间 < 2秒
- [ ] 验证码发送响应时间 < 1秒
- [ ] 支持并发用户 > 50
- [ ] 内存使用稳定

### 安全验收
- [ ] 验证码安全生成
- [ ] 邮箱验证必需
- [ ] 无密码用户安全
- [ ] 防暴力破解

## 🐛 常见问题排查

### 问题1：验证码收不到
**排查步骤：**
1. 检查后端日志是否有验证码生成记录
2. 确认邮件服务配置（生产环境）
3. 检查网络请求是否成功

### 问题2：自动注册失败
**排查步骤：**
1. 检查数据库连接
2. 查看后端错误日志
3. 验证邮箱格式是否正确

### 问题3：登录后跳转异常
**排查步骤：**
1. 检查Token是否正确生成
2. 验证前端路由配置
3. 查看浏览器控制台错误

### 问题4：界面显示异常
**排查步骤：**
1. 清除浏览器缓存
2. 检查前端构建是否成功
3. 验证CSS样式加载

## 📝 测试报告模板

```
测试日期：____
测试人员：____
测试环境：开发环境

功能测试结果：
□ 新用户自动注册：通过/失败
□ 现有用户登录：通过/失败
□ 验证码功能：通过/失败
□ 错误处理：通过/失败
□ 界面功能：通过/失败
□ 兼容性：通过/失败

性能测试结果：
□ 响应时间：____ms
□ 并发支持：____用户
□ 内存使用：____MB

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____

总体评价：通过/需要修复
```

## 🚀 部署前最终检查

- [ ] 所有测试用例通过
- [ ] 性能指标达标
- [ ] 安全检查完成
- [ ] 文档更新完整
- [ ] 代码审查通过

**测试完成后，统一验证码登录/注册系统即可投入生产使用！**
