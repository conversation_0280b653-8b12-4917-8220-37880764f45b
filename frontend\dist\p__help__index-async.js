((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__help__index'],
{ "src/pages/help/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const { Title, Paragraph, Text } = _antd.Typography;
const HelpPage = ()=>{
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.PageContainer, {
        title: "帮助中心",
        subTitle: "团队协作管理系统使用指南",
        extra: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                type: "primary",
                children: "联系技术支持"
            }, "contact", false, {
                fileName: "src/pages/help/index.tsx",
                lineNumber: 18,
                columnNumber: 9
            }, void 0)
        ],
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                maxWidth: 1200,
                margin: '0 auto'
            },
            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                direction: "vertical",
                size: "large",
                style: {
                    width: '100%'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 3,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.BookOutlined, {
                                        style: {
                                            marginRight: 8
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 28,
                                        columnNumber: 15
                                    }, this),
                                    "快速开始"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 27,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: "欢迎使用团队协作管理系统！本系统帮助您高效管理团队成员、项目和任务。"
                            }, void 0, false, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 31,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "首次使用步骤："
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 35,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ol", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                                children: "注册账号并登录系统"
                                            }, void 0, false, {
                                                fileName: "src/pages/help/index.tsx",
                                                lineNumber: 37,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                                children: "创建或加入团队"
                                            }, void 0, false, {
                                                fileName: "src/pages/help/index.tsx",
                                                lineNumber: 38,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                                children: "邀请团队成员"
                                            }, void 0, false, {
                                                fileName: "src/pages/help/index.tsx",
                                                lineNumber: 39,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                                                children: "开始协作管理"
                                            }, void 0, false, {
                                                fileName: "src/pages/help/index.tsx",
                                                lineNumber: 40,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 36,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 34,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/help/index.tsx",
                        lineNumber: 26,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 3,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                        style: {
                                            marginRight: 8
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 48,
                                        columnNumber: 15
                                    }, this),
                                    "团队管理"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 47,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "创建团队："
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 52,
                                        columnNumber: 15
                                    }, this),
                                    '在团队页面点击"创建团队"按钮，填写团队信息即可创建新团队。'
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 51,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "邀请成员："
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 56,
                                        columnNumber: 15
                                    }, this),
                                    "团队管理员可以通过邮箱邀请新成员加入团队。"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 55,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "角色权限："
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 60,
                                        columnNumber: 15
                                    }, this),
                                    "系统支持多种角色权限，包括管理员、普通成员等，确保团队协作的安全性。"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 59,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/help/index.tsx",
                        lineNumber: 46,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 3,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {
                                        style: {
                                            marginRight: 8
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 68,
                                        columnNumber: 15
                                    }, this),
                                    "系统设置"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 67,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "个人设置："
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 72,
                                        columnNumber: 15
                                    }, this),
                                    "在右上角头像菜单中可以修改个人信息、密码等设置。"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 71,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "团队设置："
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 76,
                                        columnNumber: 15
                                    }, this),
                                    "团队管理员可以在团队设置页面修改团队信息、管理成员权限。"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 75,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/help/index.tsx",
                        lineNumber: 66,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 3,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.QuestionCircleOutlined, {
                                        style: {
                                            marginRight: 8
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 84,
                                        columnNumber: 15
                                    }, this),
                                    "常见问题"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 83,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "Q: 如何切换团队？"
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 88,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 89,
                                        columnNumber: 15
                                    }, this),
                                    "A: 在顶部导航栏的团队名称处点击，可以选择切换到其他团队。"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 87,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 92,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "Q: 忘记密码怎么办？"
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 94,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 95,
                                        columnNumber: 15
                                    }, this),
                                    'A: 在登录页面点击"忘记密码"，通过邮箱重置密码。'
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 93,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 98,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "Q: 如何邀请新成员？"
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 100,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 101,
                                        columnNumber: 15
                                    }, this),
                                    "A: 团队管理员可以在团队管理页面通过邮箱邀请新成员。"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 99,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/help/index.tsx",
                        lineNumber: 82,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                level: 3,
                                children: "联系我们"
                            }, void 0, false, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 108,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: "如果您在使用过程中遇到问题，可以通过以下方式联系我们："
                            }, void 0, false, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 109,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "技术支持邮箱："
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 113,
                                        columnNumber: 15
                                    }, this),
                                    " <EMAIL>",
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 114,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "用户反馈："
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 115,
                                        columnNumber: 15
                                    }, this),
                                    " <EMAIL>",
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 116,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: "工作时间："
                                    }, void 0, false, {
                                        fileName: "src/pages/help/index.tsx",
                                        lineNumber: 117,
                                        columnNumber: 15
                                    }, this),
                                    " 周一至周五 9:00-18:00"
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/help/index.tsx",
                                lineNumber: 112,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/help/index.tsx",
                        lineNumber: 107,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/help/index.tsx",
                lineNumber: 24,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "src/pages/help/index.tsx",
            lineNumber: 23,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/help/index.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
};
_c = HelpPage;
var _default = HelpPage;
var _c;
$RefreshReg$(_c, "HelpPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__help__index-async.js.map