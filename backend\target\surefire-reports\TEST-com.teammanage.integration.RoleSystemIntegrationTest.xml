<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" version="3.0.2" name="com.teammanage.integration.RoleSystemIntegrationTest" time="4.011" tests="6" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="24"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="F:\Project\teamAuth\backend\target\test-classes;F:\Project\teamAuth\backend\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.5.3\spring-boot-starter-web-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.5.3\spring-boot-starter-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.5.3\spring-boot-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.5.3\spring-boot-starter-logging-3.5.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.5.18\logback-classic-1.5.18.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.5.18\logback-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.24.3\log4j-to-slf4j-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.17\jul-to-slf4j-2.0.17.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.4\snakeyaml-2.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.5.3\spring-boot-starter-json-3.5.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.19.1\jackson-datatype-jdk8-2.19.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.19.1\jackson-datatype-jsr310-2.19.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.19.1\jackson-module-parameter-names-2.19.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.5.3\spring-boot-starter-tomcat-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.42\tomcat-embed-core-10.1.42.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.42\tomcat-embed-websocket-10.1.42.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.8\spring-web-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.2.8\spring-beans-6.2.8.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.15.1\micrometer-observation-1.15.1.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.15.1\micrometer-commons-1.15.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.2.8\spring-webmvc-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.2.8\spring-context-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.2.8\spring-expression-6.2.8.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-jsqlparser\3.5.12\mybatis-plus-jsqlparser-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\5.1\jsqlparser-5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-jsqlparser-common\3.5.12\mybatis-plus-jsqlparser-common-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.5.12\mybatis-plus-extension-3.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\3.5.3\spring-boot-starter-security-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.2.8\spring-aop-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\6.5.1\spring-security-config-6.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\6.5.1\spring-security-web-6.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\3.5.3\spring-boot-starter-validation-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.42\tomcat-embed-el-10.1.42.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\8.0.2.Final\hibernate-validator-8.0.2.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-api\0.12.6\jjwt-api-0.12.6.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-impl\0.12.6\jjwt-impl-0.12.6.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-jackson\0.12.6\jjwt-jackson-0.12.6.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-spring-boot3-starter\3.5.12\mybatis-plus-spring-boot3-starter-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.5.12\mybatis-plus-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.5.12\mybatis-plus-core-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.5.12\mybatis-plus-annotation-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-spring\3.5.12\mybatis-plus-spring-3.5.12.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.12\mybatis-plus-spring-boot-autoconfigure-3.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.5.3\spring-boot-autoconfigure-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\3.5.3\spring-boot-starter-jdbc-3.5.3.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\6.3.0\HikariCP-6.3.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.2.8\spring-jdbc-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.2.8\spring-tx-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\mariadb\jdbc\mariadb-java-client\3.5.3\mariadb-java-client-3.5.3.jar;C:\Users\<USER>\.m2\repository\com\github\ben-manes\caffeine\caffeine\3.2.1\caffeine-3.2.1.jar;C:\Users\<USER>\.m2\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.38.0\error_prone_annotations-2.38.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-cache\3.5.3\spring-boot-starter-cache-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.2.8\spring-context-support-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-ui\2.8.9\springdoc-openapi-starter-webmvc-ui-2.8.9.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-api\2.8.9\springdoc-openapi-starter-webmvc-api-2.8.9.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-common\2.8.9\springdoc-openapi-starter-common-2.8.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core-jakarta\2.2.30\swagger-core-jakarta-2.2.30.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations-jakarta\2.2.30\swagger-annotations-jakarta-2.2.30.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models-jakarta\2.2.30\swagger-models-jakarta-2.2.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.19.1\jackson-dataformat-yaml-2.19.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\5.21.0\swagger-ui-5.21.0.jar;C:\Users\<USER>\.m2\repository\org\webjars\webjars-locator-lite\1.1.0\webjars-locator-lite-1.1.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.19.1\jackson-databind-2.19.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.19.1\jackson-annotations-2.19.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.19.1\jackson-core-2.19.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.5.3\spring-boot-starter-test-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\3.5.3\spring-boot-test-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.5.3\spring-boot-test-autoconfigure-3.5.3.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.2\json-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.2\accessors-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.7.1\asm-9.7.1.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.27.3\assertj-core-3.27.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.17.6\byte-buddy-1.17.6.jar;C:\Users\<USER>\.m2\repository\org\awaitility\awaitility\4.2.2\awaitility-4.2.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\3.0\hamcrest-3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.12.2\junit-jupiter-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.12.2\junit-jupiter-api-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.12.2\junit-platform-commons-1.12.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.12.2\junit-jupiter-params-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.12.2\junit-jupiter-engine-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.12.2\junit-platform-engine-1.12.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.17.0\mockito-core-5.17.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.17.6\byte-buddy-agent-1.17.6.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.17.0\mockito-junit-jupiter-5.17.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.2.8\spring-core-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.2.8\spring-jcl-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\6.2.8\spring-test-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.10.2\xmlunit-core-2.10.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-test\6.5.1\spring-security-test-6.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\6.5.1\spring-security-core-6.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\6.5.1\spring-security-crypto-6.5.1.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Windows 10"/>
    <property name="java.vm.specification.version" value="24"/>
    <property name="APPLICATION_NAME" value="team-manage"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-24\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire13466434344809300243\surefirebooter-20250729232623305_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire13466434344809300243 2025-07-29T23-26-23_211-jvmRun1 surefire-20250729232623305_1tmp surefire_0-20250729232623305_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="F:\Project\teamAuth\backend\target\test-classes;F:\Project\teamAuth\backend\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.5.3\spring-boot-starter-web-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.5.3\spring-boot-starter-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.5.3\spring-boot-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.5.3\spring-boot-starter-logging-3.5.3.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.5.18\logback-classic-1.5.18.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.5.18\logback-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.24.3\log4j-to-slf4j-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.17\jul-to-slf4j-2.0.17.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.4\snakeyaml-2.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.5.3\spring-boot-starter-json-3.5.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.19.1\jackson-datatype-jdk8-2.19.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.19.1\jackson-datatype-jsr310-2.19.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.19.1\jackson-module-parameter-names-2.19.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.5.3\spring-boot-starter-tomcat-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.42\tomcat-embed-core-10.1.42.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.42\tomcat-embed-websocket-10.1.42.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.8\spring-web-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.2.8\spring-beans-6.2.8.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.15.1\micrometer-observation-1.15.1.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.15.1\micrometer-commons-1.15.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.2.8\spring-webmvc-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.2.8\spring-context-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.2.8\spring-expression-6.2.8.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-jsqlparser\3.5.12\mybatis-plus-jsqlparser-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\5.1\jsqlparser-5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-jsqlparser-common\3.5.12\mybatis-plus-jsqlparser-common-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.5.12\mybatis-plus-extension-3.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\3.5.3\spring-boot-starter-security-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.2.8\spring-aop-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\6.5.1\spring-security-config-6.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\6.5.1\spring-security-web-6.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\3.5.3\spring-boot-starter-validation-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.42\tomcat-embed-el-10.1.42.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\8.0.2.Final\hibernate-validator-8.0.2.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-api\0.12.6\jjwt-api-0.12.6.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-impl\0.12.6\jjwt-impl-0.12.6.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt-jackson\0.12.6\jjwt-jackson-0.12.6.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-spring-boot3-starter\3.5.12\mybatis-plus-spring-boot3-starter-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.5.12\mybatis-plus-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.5.12\mybatis-plus-core-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.5.12\mybatis-plus-annotation-3.5.12.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-spring\3.5.12\mybatis-plus-spring-3.5.12.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.12\mybatis-plus-spring-boot-autoconfigure-3.5.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.5.3\spring-boot-autoconfigure-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\3.5.3\spring-boot-starter-jdbc-3.5.3.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\6.3.0\HikariCP-6.3.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.2.8\spring-jdbc-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.2.8\spring-tx-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\mariadb\jdbc\mariadb-java-client\3.5.3\mariadb-java-client-3.5.3.jar;C:\Users\<USER>\.m2\repository\com\github\ben-manes\caffeine\caffeine\3.2.1\caffeine-3.2.1.jar;C:\Users\<USER>\.m2\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.38.0\error_prone_annotations-2.38.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-cache\3.5.3\spring-boot-starter-cache-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.2.8\spring-context-support-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-ui\2.8.9\springdoc-openapi-starter-webmvc-ui-2.8.9.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-api\2.8.9\springdoc-openapi-starter-webmvc-api-2.8.9.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-common\2.8.9\springdoc-openapi-starter-common-2.8.9.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core-jakarta\2.2.30\swagger-core-jakarta-2.2.30.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations-jakarta\2.2.30\swagger-annotations-jakarta-2.2.30.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models-jakarta\2.2.30\swagger-models-jakarta-2.2.30.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.19.1\jackson-dataformat-yaml-2.19.1.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\5.21.0\swagger-ui-5.21.0.jar;C:\Users\<USER>\.m2\repository\org\webjars\webjars-locator-lite\1.1.0\webjars-locator-lite-1.1.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.19.1\jackson-databind-2.19.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.19.1\jackson-annotations-2.19.1.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.19.1\jackson-core-2.19.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.5.3\spring-boot-starter-test-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\3.5.3\spring-boot-test-3.5.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.5.3\spring-boot-test-autoconfigure-3.5.3.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.2\json-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.2\accessors-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.7.1\asm-9.7.1.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.27.3\assertj-core-3.27.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.17.6\byte-buddy-1.17.6.jar;C:\Users\<USER>\.m2\repository\org\awaitility\awaitility\4.2.2\awaitility-4.2.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\3.0\hamcrest-3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.12.2\junit-jupiter-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.12.2\junit-jupiter-api-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.12.2\junit-platform-commons-1.12.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.12.2\junit-jupiter-params-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.12.2\junit-jupiter-engine-5.12.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.12.2\junit-platform-engine-1.12.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.17.0\mockito-core-5.17.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.17.6\byte-buddy-agent-1.17.6.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.17.0\mockito-junit-jupiter-5.17.0.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.2.8\spring-core-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.2.8\spring-jcl-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\6.2.8\spring-test-6.2.8.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.10.2\xmlunit-core-2.10.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-test\6.5.1\spring-security-test-6.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\6.5.1\spring-security-core-6.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\6.5.1\spring-security-crypto-6.5.1.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-24"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="F:\Project\teamAuth\backend"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire13466434344809300243\surefirebooter-20250729232623305_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="24.0.1+9-30"/>
    <property name="user.name" value="X"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="com.zaxxer.hikari.pool_number" value="1"/>
    <property name="java.version" value="24.0.1"/>
    <property name="user.dir" value="F:\Project\teamAuth\backend"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="20964"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="GBK"/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-24\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\nvm;D:\nodejs;D:\Tencent\微信web开发者工具\dll;D:\maven\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\nvm;D:\nodejs;D:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin;;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="24.0.1+9-30"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="68.0"/>
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"/>
    <property name="LOGGED_APPLICATION_NAME" value="[team-manage] "/>
  </properties>
  <testcase name="testRolePermissionChecks" classname="com.teammanage.integration.RoleSystemIntegrationTest" time="0.813">
    <system-out><![CDATA[23:26:24.167 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.teammanage.integration.RoleSystemIntegrationTest]: RoleSystemIntegrationTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
23:26:24.270 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.teammanage.TeamManageApplication for test class com.teammanage.integration.RoleSystemIntegrationTest

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.5.3)

2025-07-29 23:26:24 [main] INFO  c.t.i.RoleSystemIntegrationTest - Starting RoleSystemIntegrationTest using Java 24.0.1 with PID 20964 (started by X in F:\Project\teamAuth\backend)
2025-07-29 23:26:24 [main] DEBUG c.t.i.RoleSystemIntegrationTest - Running with Spring Boot v3.5.3, Spring v6.2.8
2025-07-29 23:26:24 [main] INFO  c.t.i.RoleSystemIntegrationTest - The following 1 profile is active: "test"
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Get /192.168.10.64 network interface 
Get network interface info: name:wireless_32768 (802.11ac Wireless LAN Card)
Initialization Sequence datacenterId:6 workerId:3
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.12 
2025-07-29 23:26:26 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 24ba6dc5-7810-4393-895d-1939b03549d7

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-29 23:26:26 [main] INFO  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-07-29 23:26:26 [main] DEBUG o.s.s.web.DefaultSecurityFilterChain - Will secure any request with filters: DisableEncodeUrlFilter, WebAsyncManagerIntegrationFilter, SecurityContextHolderFilter, HeaderWriterFilter, CorsFilter, LogoutFilter, JwtAuthenticationFilter, RequestCacheAwareFilter, SecurityContextHolderAwareRequestFilter, AnonymousAuthenticationFilter, SessionManagementFilter, ExceptionTranslationFilter, AuthorizationFilter
2025-07-29 23:26:26 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.web.context.support.GenericWebApplicationContext@18388a3c
2025-07-29 23:26:27 [main] INFO  c.t.i.RoleSystemIntegrationTest - Started RoleSystemIntegrationTest in 2.619 seconds (process running for 3.414)
2025-07-29 23:26:27 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-29 23:26:27 [scheduling-1] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.mariadb.jdbc.Connection@72951db8
2025-07-29 23:26:27 [scheduling-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f547665]
JDBC Connection [HikariProxyConnection@702884628 wrapping org.mariadb.jdbc.Connection@72951db8] will be managed by Spring
==>  Preparing: UPDATE team_invitation SET status = 'EXPIRED', updated_at = ? WHERE status = 'PENDING' AND expires_at <= ?
==> Parameters: 2025-07-29T23:26:27.117667400(LocalDateTime), 2025-07-29T23:26:27.117667400(LocalDateTime)
<==    Updates: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f547665]
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f547665]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f547665]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f547665]
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d1d85d0]
JDBC Connection [HikariProxyConnection@********** wrapping org.mariadb.jdbc.Connection@67689c81] will be managed by Spring
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Creator(String), 2025-07-29T23:26:27.*********(LocalDateTime), 2025-07-29T23:26:27.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d1d85d0]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d1d85d0] from current transaction
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Member(String), 2025-07-29T23:26:27.823365(LocalDateTime), 2025-07-29T23:26:27.823365(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d1d85d0]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d1d85d0]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d1d85d0]
]]></system-out>
    <system-err><![CDATA[Mockito is currently self-attaching to enable the inline-mock-maker. This will no longer work in future releases of the JDK. Please add Mockito as an agent to your build as described in Mockito's documentation: https://javadoc.io/doc/org.mockito/mockito-core/latest/org.mockito/org/mockito/Mockito.html#0.3
WARNING: A Java agent has been loaded dynamically (C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.17.6\byte-buddy-agent-1.17.6.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
  </testcase>
  <testcase name="testTeamMemberListRoleResponse" classname="com.teammanage.integration.RoleSystemIntegrationTest" time="0.164">
    <system-out><![CDATA[Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
JDBC Connection [HikariProxyConnection@********** wrapping org.mariadb.jdbc.Connection@67689c81] will be managed by Spring
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Creator(String), 2025-07-29T23:26:27.*********(LocalDateTime), 2025-07-29T23:26:27.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Member(String), 2025-07-29T23:26:27.*********(LocalDateTime), 2025-07-29T23:26:27.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
2025-07-29 23:26:27 [main] DEBUG c.t.context.TeamContextHolder - �����Ŷ�������: teamId=null, userId=65, isCreator=true
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: SELECT * FROM account_subscription WHERE account_id = ? AND status = 'ACTIVE' AND (end_date IS NULL OR end_date >= CURDATE()) ORDER BY created_at DESC LIMIT 1
==> Parameters: 65(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM team WHERE is_deleted=0 AND (created_by = ? AND is_deleted = ?)
==> Parameters: 65(Long), false(Boolean)
<==    Columns: total
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: SELECT COUNT(1) FROM team WHERE name = ? AND is_deleted = 0
==> Parameters: Test Team(String)
<==    Columns: COUNT(1)
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: INSERT INTO team ( name, description, created_by, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ? )
==> Parameters: Test Team(String), Test Description(String), 65(Long), false(Boolean), 2025-07-29T23:26:27.*********(LocalDateTime), 2025-07-29T23:26:27.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: INSERT INTO team_member ( team_id, account_id, is_creator, role, assigned_at, last_access_time, is_active, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 21(Long), 65(Long), true(Boolean), TEAM_CREATOR(String), 2025-07-29T23:26:27.*********(LocalDateTime), 2025-07-29T23:26:27.*********(LocalDateTime), true(Boolean), false(Boolean), 2025-07-29T23:26:27.*********(LocalDateTime), 2025-07-29T23:26:27.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
2025-07-29 23:26:27 [main] INFO  com.teammanage.service.TeamService - �ŶӴ����ɹ�: teamId=21, name=Test Team, creatorId=65, currentCount=1/1
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
==> Parameters: 21(Long)
<==    Columns: id, name, description, created_by, is_deleted, created_at, updated_at
<==        Row: 21, Test Team, Test Description, 65, 0, 2025-07-29 23:26:27, 2025-07-29 23:26:27
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
==> Parameters: 21(Long)
<==    Columns: COUNT(1)
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: INSERT INTO team_member ( team_id, account_id, is_creator, role, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ? )
==> Parameters: 21(Long), 66(Long), false(Boolean), TEAM_MEMBER(String), 2025-07-29T23:26:27.*********(LocalDateTime), 2025-07-29T23:26:27.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
2025-07-29 23:26:27 [main] DEBUG c.t.context.TeamContextHolder - �����Ŷ�������: teamId=21, userId=65, isCreator=true
Load compatibleSet: com.baomidou.mybatisplus.extension.spi.SpringCompatibleSet@c7c07ff
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: SELECT COUNT(*) AS total FROM team_member WHERE is_deleted = 0 AND (team_id = ? AND is_active = ? AND is_deleted = ?)
==> Parameters: 21(Long), true(Boolean), false(Boolean)
<==    Columns: total
<==        Row: 2
<==      Total: 1
==>  Preparing: SELECT id,team_id,account_id,is_creator,role,assigned_at,last_access_time,is_active,is_deleted,created_at,updated_at FROM team_member WHERE is_deleted=0 AND (team_id = ? AND is_active = ? AND is_deleted = ?) ORDER BY is_creator DESC,assigned_at DESC LIMIT ?
==> Parameters: 21(Long), true(Boolean), false(Boolean), 10(Long)
<==    Columns: id, team_id, account_id, is_creator, role, assigned_at, last_access_time, is_active, is_deleted, created_at, updated_at
<==        Row: 24, 21, 65, 1, TEAM_CREATOR, 2025-07-29 23:26:27, 2025-07-29 23:26:27, 1, 0, 2025-07-29 23:26:27, 2025-07-29 23:26:27
<==        Row: 25, 21, 66, 0, TEAM_MEMBER, 2025-07-29 23:26:27, null, 1, 0, 2025-07-29 23:26:27, 2025-07-29 23:26:27
<==      Total: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
==> Parameters: 65(Long)
<==    Columns: id, email, password_hash, name, telephone, default_subscription_plan_id, created_at, updated_at
<==        Row: 65, <EMAIL>, hashedPassword, Team Creator, null, null, 2025-07-29 23:26:27, 2025-07-29 23:26:27
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b] from current transaction
==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
==> Parameters: 66(Long)
<==    Columns: id, email, password_hash, name, telephone, default_subscription_plan_id, created_at, updated_at
<==        Row: 66, <EMAIL>, hashedPassword, Team Member, null, null, 2025-07-29 23:26:27, 2025-07-29 23:26:27
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
2025-07-29 23:26:28 [main] DEBUG c.t.context.TeamContextHolder - �����Ŷ�������
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24f2608b]
]]></system-out>
  </testcase>
  <testcase name="testTeamCreationRoleAssignment" classname="com.teammanage.integration.RoleSystemIntegrationTest" time="0.016">
    <system-out><![CDATA[Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
JDBC Connection [HikariProxyConnection@********** wrapping org.mariadb.jdbc.Connection@67689c81] will be managed by Spring
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Creator(String), 2025-07-29T23:26:28.010894(LocalDateTime), 2025-07-29T23:26:28.010894(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336] from current transaction
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Member(String), 2025-07-29T23:26:28.011893(LocalDateTime), 2025-07-29T23:26:28.011893(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
2025-07-29 23:26:28 [main] DEBUG c.t.context.TeamContextHolder - �����Ŷ�������: teamId=null, userId=67, isCreator=true
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336] from current transaction
==>  Preparing: SELECT * FROM account_subscription WHERE account_id = ? AND status = 'ACTIVE' AND (end_date IS NULL OR end_date >= CURDATE()) ORDER BY created_at DESC LIMIT 1
==> Parameters: 67(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM team WHERE is_deleted=0 AND (created_by = ? AND is_deleted = ?)
==> Parameters: 67(Long), false(Boolean)
<==    Columns: total
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336] from current transaction
==>  Preparing: SELECT COUNT(1) FROM team WHERE name = ? AND is_deleted = 0
==> Parameters: Test Team(String)
<==    Columns: COUNT(1)
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336] from current transaction
==>  Preparing: INSERT INTO team ( name, description, created_by, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ? )
==> Parameters: Test Team(String), Test Description(String), 67(Long), false(Boolean), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336] from current transaction
==>  Preparing: INSERT INTO team_member ( team_id, account_id, is_creator, role, assigned_at, last_access_time, is_active, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 22(Long), 67(Long), true(Boolean), TEAM_CREATOR(String), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime), true(Boolean), false(Boolean), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
2025-07-29 23:26:28 [main] INFO  com.teammanage.service.TeamService - �ŶӴ����ɹ�: teamId=22, name=Test Team, creatorId=67, currentCount=1/1
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336] from current transaction
==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
==> Parameters: 22(Long)
<==    Columns: id, name, description, created_by, is_deleted, created_at, updated_at
<==        Row: 22, Test Team, Test Description, 67, 0, 2025-07-29 23:26:28, 2025-07-29 23:26:28
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336] from current transaction
==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
==> Parameters: 22(Long)
<==    Columns: COUNT(1)
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336] from current transaction
==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
==> Parameters: 22(Long), 67(Long)
<==    Columns: id, team_id, account_id, is_creator, role, assigned_at, last_access_time, is_active, is_deleted, updated_at, created_at
<==        Row: 26, 22, 67, 1, TEAM_CREATOR, 2025-07-29 23:26:28, 2025-07-29 23:26:28, 1, 0, 2025-07-29 23:26:28, 2025-07-29 23:26:28
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
2025-07-29 23:26:28 [main] DEBUG c.t.context.TeamContextHolder - �����Ŷ�������
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7fcbc336]
]]></system-out>
  </testcase>
  <testcase name="testRoleEnumExtensibility" classname="com.teammanage.integration.RoleSystemIntegrationTest" time="0.01">
    <system-out><![CDATA[Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ebc6d8b]
JDBC Connection [HikariProxyConnection@********** wrapping org.mariadb.jdbc.Connection@67689c81] will be managed by Spring
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Creator(String), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ebc6d8b]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ebc6d8b] from current transaction
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Member(String), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ebc6d8b]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ebc6d8b]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ebc6d8b]
]]></system-out>
  </testcase>
  <testcase name="testBackwardCompatibility" classname="com.teammanage.integration.RoleSystemIntegrationTest" time="0.007">
    <system-out><![CDATA[Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4229b92c]
JDBC Connection [HikariProxyConnection@********** wrapping org.mariadb.jdbc.Connection@67689c81] will be managed by Spring
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Creator(String), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4229b92c]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4229b92c] from current transaction
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Member(String), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4229b92c]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4229b92c]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4229b92c]
]]></system-out>
  </testcase>
  <testcase name="testInvitationDefaultRoleAssignment" classname="com.teammanage.integration.RoleSystemIntegrationTest" time="0.024">
    <system-out><![CDATA[Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
JDBC Connection [HikariProxyConnection@********** wrapping org.mariadb.jdbc.Connection@67689c81] will be managed by Spring
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Creator(String), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: INSERT INTO account ( email, password_hash, name, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ? )
==> Parameters: <EMAIL>(String), hashedPassword(String), Team Member(String), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
2025-07-29 23:26:28 [main] DEBUG c.t.context.TeamContextHolder - �����Ŷ�������: teamId=null, userId=73, isCreator=true
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: SELECT * FROM account_subscription WHERE account_id = ? AND status = 'ACTIVE' AND (end_date IS NULL OR end_date >= CURDATE()) ORDER BY created_at DESC LIMIT 1
==> Parameters: 73(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: SELECT COUNT( * ) AS total FROM team WHERE is_deleted=0 AND (created_by = ? AND is_deleted = ?)
==> Parameters: 73(Long), false(Boolean)
<==    Columns: total
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: SELECT COUNT(1) FROM team WHERE name = ? AND is_deleted = 0
==> Parameters: Test Team(String)
<==    Columns: COUNT(1)
<==        Row: 0
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: INSERT INTO team ( name, description, created_by, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ? )
==> Parameters: Test Team(String), Test Description(String), 73(Long), false(Boolean), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: INSERT INTO team_member ( team_id, account_id, is_creator, role, assigned_at, last_access_time, is_active, is_deleted, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 23(Long), 73(Long), true(Boolean), TEAM_CREATOR(String), 2025-07-29T23:26:28.053903(LocalDateTime), 2025-07-29T23:26:28.053903(LocalDateTime), true(Boolean), false(Boolean), 2025-07-29T23:26:28.053903(LocalDateTime), 2025-07-29T23:26:28.053903(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
2025-07-29 23:26:28 [main] INFO  com.teammanage.service.TeamService - �ŶӴ����ɹ�: teamId=23, name=Test Team, creatorId=73, currentCount=1/1
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: SELECT id,name,description,created_by,is_deleted,created_at,updated_at FROM team WHERE id=? AND is_deleted=0
==> Parameters: 23(Long)
<==    Columns: id, name, description, created_by, is_deleted, created_at, updated_at
<==        Row: 23, Test Team, Test Description, 73, 0, 2025-07-29 23:26:28, 2025-07-29 23:26:28
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: SELECT COUNT(1) FROM team_member WHERE team_id = ? AND is_active = 1 AND is_deleted = 0
==> Parameters: 23(Long)
<==    Columns: COUNT(1)
<==        Row: 1
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
2025-07-29 23:26:28 [main] DEBUG c.t.context.TeamContextHolder - �����Ŷ�������: teamId=23, userId=73, isCreator=true
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: SELECT id,email,password_hash,name,telephone,default_subscription_plan_id,created_at,updated_at FROM account WHERE id=?
==> Parameters: 73(Long)
<==    Columns: id, email, password_hash, name, telephone, default_subscription_plan_id, created_at, updated_at
<==        Row: 73, <EMAIL>, hashedPassword, Team Creator, null, null, 2025-07-29 23:26:28, 2025-07-29 23:26:28
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: SELECT * FROM account WHERE email = ?
==> Parameters: <EMAIL>(String)
<==    Columns: id, email, password_hash, name, default_subscription_plan_id, created_at, updated_at, telephone
<==        Row: 74, <EMAIL>, hashedPassword, Team Member, null, 2025-07-29 23:26:28, 2025-07-29 23:26:28, null
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
==> Parameters: 23(Long), 74(Long)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: SELECT * FROM team_invitation WHERE team_id = ? AND invitee_email = ? AND status = 'PENDING' AND expires_at > NOW()
==> Parameters: 23(Long), <EMAIL>(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: INSERT INTO team_invitation ( team_id, inviter_id, invitee_email, invitee_id, status, invited_at, expires_at, message, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
==> Parameters: 23(Long), 73(Long), <EMAIL>(String), 74(Long), PENDING(String), 2025-07-29T23:26:28.057902400(LocalDateTime), 2025-08-01T23:26:28.057902400(LocalDateTime), Welcome to the team!(String), 2025-07-29T23:26:28.061903600(LocalDateTime), 2025-07-29T23:26:28.061903600(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
2025-07-29 23:26:28 [main] INFO  c.t.service.TeamInvitationService - ���봴���ɹ�: invitationId=5, teamId=23, email=<EMAIL>
2025-07-29 23:26:28 [main] INFO  c.t.service.TeamInvitationService - ���������������: teamId=23, inviterId=73, totalEmails=1, successCount=1
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: INSERT INTO team_member ( team_id, account_id, is_creator, role, created_at, updated_at ) VALUES ( ?, ?, ?, ?, ?, ? )
==> Parameters: 23(Long), 74(Long), false(Boolean), TEAM_MEMBER(String), 2025-07-29T23:26:28.*********(LocalDateTime), 2025-07-29T23:26:28.*********(LocalDateTime)
<==    Updates: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698] from current transaction
==>  Preparing: SELECT * FROM team_member WHERE team_id = ? AND account_id = ? AND is_deleted = 0
==> Parameters: 23(Long), 74(Long)
<==    Columns: id, team_id, account_id, is_creator, role, assigned_at, last_access_time, is_active, is_deleted, updated_at, created_at
<==        Row: 28, 23, 74, 0, TEAM_MEMBER, 2025-07-29 23:26:28, null, 1, 0, 2025-07-29 23:26:28, 2025-07-29 23:26:28
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
2025-07-29 23:26:28 [main] DEBUG c.t.context.TeamContextHolder - �����Ŷ�������
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54ef9698]
]]></system-out>
  </testcase>
</testsuite>