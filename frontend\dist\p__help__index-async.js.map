{"version": 3, "sources": ["src/pages/help/index.tsx"], "sourcesContent": ["import {\n  BookOutlined,\n  QuestionCircleOutlined,\n  SettingOutlined,\n  TeamOutlined,\n} from '@ant-design/icons';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { Button, Card, Divider, Space, Typography } from 'antd';\n\nconst { Title, Paragraph, Text } = Typography;\n\nconst HelpPage: React.FC = () => {\n  return (\n    <PageContainer\n      title=\"帮助中心\"\n      subTitle=\"团队协作管理系统使用指南\"\n      extra={[\n        <Button key=\"contact\" type=\"primary\">\n          联系技术支持\n        </Button>,\n      ]}\n    >\n      <div style={{ maxWidth: 1200, margin: '0 auto' }}>\n        <Space direction=\"vertical\" size=\"large\" style={{ width: '100%' }}>\n          {/* 快速开始 */}\n          <Card>\n            <Title level={3}>\n              <BookOutlined style={{ marginRight: 8 }} />\n              快速开始\n            </Title>\n            <Paragraph>\n              欢迎使用团队协作管理系统！本系统帮助您高效管理团队成员、项目和任务。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>首次使用步骤：</Text>\n              <ol>\n                <li>注册账号并登录系统</li>\n                <li>创建或加入团队</li>\n                <li>邀请团队成员</li>\n                <li>开始协作管理</li>\n              </ol>\n            </Paragraph>\n          </Card>\n\n          {/* 团队管理 */}\n          <Card>\n            <Title level={3}>\n              <TeamOutlined style={{ marginRight: 8 }} />\n              团队管理\n            </Title>\n            <Paragraph>\n              <Text strong>创建团队：</Text>\n              在团队页面点击\"创建团队\"按钮，填写团队信息即可创建新团队。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>邀请成员：</Text>\n              团队管理员可以通过邮箱邀请新成员加入团队。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>角色权限：</Text>\n              系统支持多种角色权限，包括管理员、普通成员等，确保团队协作的安全性。\n            </Paragraph>\n          </Card>\n\n          {/* 系统设置 */}\n          <Card>\n            <Title level={3}>\n              <SettingOutlined style={{ marginRight: 8 }} />\n              系统设置\n            </Title>\n            <Paragraph>\n              <Text strong>个人设置：</Text>\n              在右上角头像菜单中可以修改个人信息、密码等设置。\n            </Paragraph>\n            <Paragraph>\n              <Text strong>团队设置：</Text>\n              团队管理员可以在团队设置页面修改团队信息、管理成员权限。\n            </Paragraph>\n          </Card>\n\n          {/* 常见问题 */}\n          <Card>\n            <Title level={3}>\n              <QuestionCircleOutlined style={{ marginRight: 8 }} />\n              常见问题\n            </Title>\n            <Paragraph>\n              <Text strong>Q: 如何切换团队？</Text>\n              <br />\n              A: 在顶部导航栏的团队名称处点击，可以选择切换到其他团队。\n            </Paragraph>\n            <Divider />\n            <Paragraph>\n              <Text strong>Q: 忘记密码怎么办？</Text>\n              <br />\n              A: 在登录页面点击\"忘记密码\"，通过邮箱重置密码。\n            </Paragraph>\n            <Divider />\n            <Paragraph>\n              <Text strong>Q: 如何邀请新成员？</Text>\n              <br />\n              A: 团队管理员可以在团队管理页面通过邮箱邀请新成员。\n            </Paragraph>\n          </Card>\n\n          {/* 联系我们 */}\n          <Card>\n            <Title level={3}>联系我们</Title>\n            <Paragraph>\n              如果您在使用过程中遇到问题，可以通过以下方式联系我们：\n            </Paragraph>\n            <Paragraph>\n              <Text strong>技术支持邮箱：</Text> <EMAIL>\n              <br />\n              <Text strong>用户反馈：</Text> <EMAIL>\n              <br />\n              <Text strong>工作时间：</Text> 周一至周五 9:00-18:00\n            </Paragraph>\n          </Card>\n        </Space>\n      </div>\n    </PageContainer>\n  );\n};\n\nexport default HelpPage;\n"], "names": [], "mappings": ";;;;;;;4BA6HA;;;eAAA;;;;;;8BAxHO;sCACuB;6BAC2B;;;;;;;;;AAEzD,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,gBAAU;AAE7C,MAAM,WAAqB;IACzB,qBACE,2BAAC,4BAAa;QACZ,OAAM;QACN,UAAS;QACT,OAAO;0BACL,2BAAC,YAAM;gBAAe,MAAK;0BAAU;eAAzB;;;;;SAGb;kBAED,cAAA,2BAAC;YAAI,OAAO;gBAAE,UAAU;gBAAM,QAAQ;YAAS;sBAC7C,cAAA,2BAAC,WAAK;gBAAC,WAAU;gBAAW,MAAK;gBAAQ,OAAO;oBAAE,OAAO;gBAAO;;kCAE9D,2BAAC,UAAI;;0CACH,2BAAC;gCAAM,OAAO;;kDACZ,2BAAC,mBAAY;wCAAC,OAAO;4CAAE,aAAa;wCAAE;;;;;;oCAAK;;;;;;;0CAG7C,2BAAC;0CAAU;;;;;;0CAGX,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,2BAAC;;0DACC,2BAAC;0DAAG;;;;;;0DACJ,2BAAC;0DAAG;;;;;;0DACJ,2BAAC;0DAAG;;;;;;0DACJ,2BAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAMV,2BAAC,UAAI;;0CACH,2BAAC;gCAAM,OAAO;;kDACZ,2BAAC,mBAAY;wCAAC,OAAO;4CAAE,aAAa;wCAAE;;;;;;oCAAK;;;;;;;0CAG7C,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;oCAAY;;;;;;;0CAG3B,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;oCAAY;;;;;;;0CAG3B,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;oCAAY;;;;;;;;;;;;;kCAM7B,2BAAC,UAAI;;0CACH,2BAAC;gCAAM,OAAO;;kDACZ,2BAAC,sBAAe;wCAAC,OAAO;4CAAE,aAAa;wCAAE;;;;;;oCAAK;;;;;;;0CAGhD,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;oCAAY;;;;;;;0CAG3B,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;oCAAY;;;;;;;;;;;;;kCAM7B,2BAAC,UAAI;;0CACH,2BAAC;gCAAM,OAAO;;kDACZ,2BAAC,6BAAsB;wCAAC,OAAO;4CAAE,aAAa;wCAAE;;;;;;oCAAK;;;;;;;0CAGvD,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,2BAAC;;;;;oCAAK;;;;;;;0CAGR,2BAAC,aAAO;;;;;0CACR,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,2BAAC;;;;;oCAAK;;;;;;;0CAGR,2BAAC,aAAO;;;;;0CACR,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;kDACb,2BAAC;;;;;oCAAK;;;;;;;;;;;;;kCAMV,2BAAC,UAAI;;0CACH,2BAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,2BAAC;0CAAU;;;;;;0CAGX,2BAAC;;kDACC,2BAAC;wCAAK,MAAM;kDAAC;;;;;;oCAAc;kDAC3B,2BAAC;;;;;kDACD,2BAAC;wCAAK,MAAM;kDAAC;;;;;;oCAAY;kDACzB,2BAAC;;;;;kDACD,2BAAC;wCAAK,MAAM;kDAAC;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC;KAhHM;IAkHN,WAAe"}