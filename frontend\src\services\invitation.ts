/**
 * 团队邀请相关 API 服务
 */

import type {
  TeamInvitationResponse,
  RespondInvitationRequest,
  SendInvitationResponse,
  AcceptInvitationByLinkRequest,
  AcceptInvitationByLinkResponse,
} from '@/types/api';
import { apiRequest } from '@/utils/request';

/**
 * 邀请服务类
 */
export class InvitationService {
  /**
   * 获取当前团队的邀请列表（需要 Team Token，仅创建者）
   */
  static async getCurrentTeamInvitations(): Promise<TeamInvitationResponse[]> {
    const response = await apiRequest.get<TeamInvitationResponse[]>('/teams/current/invitations');
    return response.data;
  }

  /**
   * 获取用户收到的邀请列表（需要 Account Token）
   */
  static async getUserReceivedInvitations(): Promise<TeamInvitationResponse[]> {
    const response = await apiRequest.get<TeamInvitationResponse[]>('/invitations/user/received');
    return response.data;
  }

  /**
   * 获取用户收到的待处理邀请列表（需要 Account Token）
   */
  static async getUserPendingInvitations(): Promise<TeamInvitationResponse[]> {
    const response = await apiRequest.get<TeamInvitationResponse[]>('/invitations/user/pending');
    return response.data;
  }

  /**
   * 响应邀请（需要 Account Token）
   */
  static async respondToInvitation(
    invitationId: number,
    data: RespondInvitationRequest,
  ): Promise<void> {
    await apiRequest.post<void>(`/invitations/${invitationId}/respond`, data);
  }

  /**
   * 取消邀请（需要 Team Token，仅邀请人）
   */
  static async cancelInvitation(invitationId: number): Promise<void> {
    await apiRequest.delete<void>(`/invitations/${invitationId}`);
  }

  /**
   * 获取邀请详情
   */
  static async getInvitationDetail(invitationId: number): Promise<TeamInvitationResponse> {
    const response = await apiRequest.get<TeamInvitationResponse>(`/invitations/${invitationId}`);
    return response.data;
  }

  /**
   * 发送邀请并生成邀请链接（需要 Team Token，仅创建者）
   */
  static async sendInvitations(data: { emails: string[]; message?: string }): Promise<SendInvitationResponse> {
    const response = await apiRequest.post<SendInvitationResponse>('/invitations/send', data);
    return response.data;
  }

  /**
   * 通过邀请链接接受邀请（公开接口）
   */
  static async acceptInvitationByLink(
    token: string,
    data: AcceptInvitationByLinkRequest
  ): Promise<AcceptInvitationByLinkResponse> {
    const response = await apiRequest.post<AcceptInvitationByLinkResponse>(
      `/invitations/accept-by-link/${token}`,
      data
    );
    return response.data;
  }

  /**
   * 更新过期邀请状态（系统内部接口）
   */
  static async updateExpiredInvitations(): Promise<number> {
    const response = await apiRequest.post<number>('/invitations/system/update-expired');
    return response.data;
  }
}

// 默认导出
export default InvitationService;
