import { TeamRole } from '@/types/api';
import {
  isTeamCreator,
  isTeamMember,
  getRoleDisplayName,
  getRoleTagColor,
  getUserRole,
  canManageTeam,
  canManageMembers,
  canAccessData,
  hasPermissionLevel,
  filterTeamCreators,
  filterTeamMembers,
  countRoles,
} from '../roleUtils';

describe('roleUtils', () => {
  // 测试数据
  const creatorWithRole = { role: TeamRole.TEAM_CREATOR, isCreator: true };
  const memberWithRole = { role: TeamRole.TEAM_MEMBER, isCreator: false };
  const creatorWithoutRole = { isCreator: true } as any;
  const memberWithoutRole = { isCreator: false } as any;

  describe('isTeamCreator', () => {
    it('should return true for team creator with role', () => {
      expect(isTeamCreator(creatorWithRole)).toBe(true);
    });

    it('should return false for team member with role', () => {
      expect(isTeamCreator(memberWithRole)).toBe(false);
    });

    it('should fallback to isCreator when role is undefined', () => {
      expect(isTeamCreator(creatorWithoutRole)).toBe(true);
      expect(isTeamCreator(memberWithoutRole)).toBe(false);
    });
  });

  describe('isTeamMember', () => {
    it('should return false for team creator with role', () => {
      expect(isTeamMember(creatorWithRole)).toBe(false);
    });

    it('should return true for team member with role', () => {
      expect(isTeamMember(memberWithRole)).toBe(true);
    });

    it('should fallback to isCreator when role is undefined', () => {
      expect(isTeamMember(creatorWithoutRole)).toBe(false);
      expect(isTeamMember(memberWithoutRole)).toBe(true);
    });
  });

  describe('getRoleDisplayName', () => {
    it('should return correct display names', () => {
      expect(getRoleDisplayName(creatorWithRole)).toBe('团队创建者');
      expect(getRoleDisplayName(memberWithRole)).toBe('团队成员');
      expect(getRoleDisplayName(creatorWithoutRole)).toBe('团队创建者');
      expect(getRoleDisplayName(memberWithoutRole)).toBe('团队成员');
    });
  });

  describe('getRoleTagColor', () => {
    it('should return correct tag colors', () => {
      expect(getRoleTagColor(creatorWithRole)).toBe('gold');
      expect(getRoleTagColor(memberWithRole)).toBe('blue');
      expect(getRoleTagColor(creatorWithoutRole)).toBe('gold');
      expect(getRoleTagColor(memberWithoutRole)).toBe('blue');
    });
  });

  describe('getUserRole', () => {
    it('should return role when available', () => {
      expect(getUserRole(creatorWithRole)).toBe(TeamRole.TEAM_CREATOR);
      expect(getUserRole(memberWithRole)).toBe(TeamRole.TEAM_MEMBER);
    });

    it('should infer role from isCreator when role is undefined', () => {
      expect(getUserRole(creatorWithoutRole)).toBe(TeamRole.TEAM_CREATOR);
      expect(getUserRole(memberWithoutRole)).toBe(TeamRole.TEAM_MEMBER);
    });
  });

  describe('permission checks', () => {
    it('should check team management permissions correctly', () => {
      expect(canManageTeam(creatorWithRole)).toBe(true);
      expect(canManageTeam(memberWithRole)).toBe(false);
      expect(canManageTeam(creatorWithoutRole)).toBe(true);
      expect(canManageTeam(memberWithoutRole)).toBe(false);
    });

    it('should check member management permissions correctly', () => {
      expect(canManageMembers(creatorWithRole)).toBe(true);
      expect(canManageMembers(memberWithRole)).toBe(false);
      expect(canManageMembers(creatorWithoutRole)).toBe(true);
      expect(canManageMembers(memberWithoutRole)).toBe(false);
    });

    it('should allow data access for all users', () => {
      expect(canAccessData(creatorWithRole)).toBe(true);
      expect(canAccessData(memberWithRole)).toBe(true);
      expect(canAccessData(creatorWithoutRole)).toBe(true);
      expect(canAccessData(memberWithoutRole)).toBe(true);
    });
  });

  describe('hasPermissionLevel', () => {
    it('should check permission levels correctly', () => {
      // Creator has higher permission than member
      expect(hasPermissionLevel(creatorWithRole, TeamRole.TEAM_MEMBER)).toBe(true);
      expect(hasPermissionLevel(creatorWithRole, TeamRole.TEAM_CREATOR)).toBe(true);
      
      // Member doesn't have creator permission
      expect(hasPermissionLevel(memberWithRole, TeamRole.TEAM_CREATOR)).toBe(false);
      expect(hasPermissionLevel(memberWithRole, TeamRole.TEAM_MEMBER)).toBe(true);
    });
  });

  describe('filtering functions', () => {
    const users = [creatorWithRole, memberWithRole, creatorWithoutRole, memberWithoutRole];

    it('should filter team creators correctly', () => {
      const creators = filterTeamCreators(users);
      expect(creators).toHaveLength(2);
      expect(creators).toContain(creatorWithRole);
      expect(creators).toContain(creatorWithoutRole);
    });

    it('should filter team members correctly', () => {
      const members = filterTeamMembers(users);
      expect(members).toHaveLength(2);
      expect(members).toContain(memberWithRole);
      expect(members).toContain(memberWithoutRole);
    });
  });

  describe('countRoles', () => {
    it('should count roles correctly', () => {
      const users = [creatorWithRole, memberWithRole, creatorWithoutRole, memberWithoutRole];
      const counts = countRoles(users);
      
      expect(counts.creators).toBe(2);
      expect(counts.members).toBe(2);
      expect(counts.total).toBe(4);
    });

    it('should handle empty array', () => {
      const counts = countRoles([]);
      expect(counts.creators).toBe(0);
      expect(counts.members).toBe(0);
      expect(counts.total).toBe(0);
    });
  });
});
