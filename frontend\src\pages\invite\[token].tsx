/**
 * 邀请链接处理页面
 * 路由: /invite/:token
 */

import { LockOutlined, MailOutlined, UserOutlined, TeamOutlined } from '@ant-design/icons';
import { Helmet, history, useParams } from '@umijs/max';
import {
  Button,
  Card,
  Form,
  Input,
  message,
  Space,
  Typography,
  Result,
  Spin,
  Alert,
} from 'antd';
import { createStyles } from 'antd-style';
import React, { useState, useEffect } from 'react';
import { Footer } from '@/components';
import { InvitationService } from '@/services';
import type { AcceptInvitationByLinkRequest } from '@/types/api';
import Settings from '../../../config/defaultSettings';

const { Title, Text, Paragraph } = Typography;

const useStyles = createStyles(({ token }) => {
  return {
    container: {
      display: 'flex',
      flexDirection: 'column',
      height: '100vh',
      overflow: 'auto',
      backgroundImage:
        "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
      backgroundSize: '100% 100%',
    },
    content: {
      flex: 1,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '32px 16px',
    },
    header: {
      marginBottom: 40,
      textAlign: 'center',
    },
    logo: {
      marginBottom: 16,
    },
    title: {
      marginBottom: 0,
    },
    inviteCard: {
      width: '100%',
      maxWidth: 500,
      boxShadow: token.boxShadowTertiary,
    },
    footer: {
      marginTop: 40,
      textAlign: 'center',
    },
  };
});

const InvitePage: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [isNewUser, setIsNewUser] = useState<boolean | null>(null);
  const { styles } = useStyles();

  // 自定义邮箱验证函数
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 处理邀请接受（现有用户）
  const handleAcceptAsExistingUser = async () => {
    if (!token) return;
    
    setProcessing(true);
    try {
      const response = await InvitationService.acceptInvitationByLink(token, {});
      
      if (response.success) {
        setResult({
          type: 'success',
          title: '加入成功！',
          message: response.nextAction || '您已成功加入团队',
          teamName: response.teamName,
          isNewUser: response.isNewUser,
        });
      } else {
        setResult({
          type: 'error',
          title: '加入失败',
          message: response.errorMessage || '处理邀请时发生错误',
        });
      }
    } catch (error) {
      console.error('处理邀请失败:', error);
      setResult({
        type: 'error',
        title: '加入失败',
        message: '网络错误，请稍后重试',
      });
    } finally {
      setProcessing(false);
    }
  };

  // 处理新用户注册并加入
  const handleRegisterAndJoin = async (values: AcceptInvitationByLinkRequest) => {
    if (!token) return;
    
    setProcessing(true);
    try {
      const response = await InvitationService.acceptInvitationByLink(token, values);
      
      if (response.success) {
        setResult({
          type: 'success',
          title: '注册并加入成功！',
          message: response.nextAction || '您已成功注册并加入团队',
          teamName: response.teamName,
          isNewUser: response.isNewUser,
        });
      } else {
        setResult({
          type: 'error',
          title: '注册失败',
          message: response.errorMessage || '注册时发生错误',
        });
      }
    } catch (error) {
      console.error('注册失败:', error);
      setResult({
        type: 'error',
        title: '注册失败',
        message: '网络错误，请稍后重试',
      });
    } finally {
      setProcessing(false);
    }
  };

  // 新用户注册表单
  const NewUserForm = () => (
    <Form
      name="newUserJoin"
      size="large"
      onFinish={handleRegisterAndJoin}
      autoComplete="off"
      layout="vertical"
    >
      <Alert
        message="您需要注册一个新账号来加入团队"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />
      
      <Form.Item
        label="姓名"
        name="name"
        rules={[
          { required: true, message: '请输入您的姓名！' },
          { max: 100, message: '姓名长度不能超过100字符！' },
        ]}
      >
        <Input
          prefix={<UserOutlined />}
          placeholder="请输入您的姓名"
        />
      </Form.Item>

      <Form.Item
        label="邮箱"
        name="email"
        rules={[
          { required: true, message: '请输入邮箱！' },
          {
            validator: (_, value) => {
              if (!value || validateEmail(value)) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('请输入有效的邮箱地址！'));
            },
          },
        ]}
      >
        <Input
          prefix={<MailOutlined />}
          placeholder="请输入您的邮箱"
        />
      </Form.Item>

      <Form.Item
        label="密码"
        name="password"
        rules={[
          { required: true, message: '请输入密码！' },
          { min: 8, message: '密码长度至少8位！' },
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="请设置密码（至少8位）"
        />
      </Form.Item>

      <Form.Item
        label="留言（可选）"
        name="message"
      >
        <Input.TextArea
          placeholder="向团队说点什么..."
          rows={3}
          maxLength={200}
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={processing} block size="large">
          注册并加入团队
        </Button>
      </Form.Item>
    </Form>
  );

  // 现有用户加入界面
  const ExistingUserJoin = () => (
    <div style={{ textAlign: 'center', padding: '40px 20px' }}>
      <TeamOutlined style={{ fontSize: 64, color: '#1890ff', marginBottom: 24 }} />
      <Title level={3}>欢迎加入团队！</Title>
      <Paragraph type="secondary">
        检测到您已有账号，点击下方按钮即可加入团队
      </Paragraph>
      <Button 
        type="primary" 
        size="large" 
        loading={processing}
        onClick={handleAcceptAsExistingUser}
        style={{ marginTop: 16 }}
      >
        加入团队
      </Button>
    </div>
  );

  // 结果展示
  const ResultDisplay = () => {
    if (!result) return null;

    return (
      <Result
        status={result.type}
        title={result.title}
        subTitle={result.message}
        extra={[
          <Button type="primary" key="login" onClick={() => history.push('/user/login')}>
            前往登录
          </Button>,
          <Button key="home" onClick={() => history.push('/')}>
            返回首页
          </Button>,
        ]}
      />
    );
  };

  // 检查token有效性（这里简化处理，实际可以调用API验证）
  useEffect(() => {
    if (!token) {
      setResult({
        type: 'error',
        title: '邀请链接无效',
        message: '邀请链接格式错误或已过期',
      });
    }
  }, [token]);

  if (result) {
    return (
      <div className={styles.container}>
        <Helmet>
          <title>
            团队邀请
            {Settings.title && ` - ${Settings.title}`}
          </title>
        </Helmet>
        <div className={styles.content}>
          <Card className={styles.inviteCard}>
            <ResultDisplay />
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Helmet>
        <title>
          团队邀请
          {Settings.title && ` - ${Settings.title}`}
        </title>
      </Helmet>
      <div className={styles.content}>
        <div className={styles.header}>
          <Space direction="vertical" align="center" size="large">
            <div className={styles.logo}>
              <img src="/logo.svg" alt="TeamAuth" height={48} />
            </div>
            <div className={styles.title}>
              <Title level={2}>团队邀请</Title>
              <Text type="secondary">加入团队，开始协作</Text>
            </div>
          </Space>
        </div>

        <Card className={styles.inviteCard}>
          {isNewUser === null ? (
            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
              <Title level={4}>选择加入方式</Title>
              <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Button 
                  type="primary" 
                  size="large" 
                  block
                  onClick={() => setIsNewUser(false)}
                >
                  我已有账号，直接加入
                </Button>
                <Button 
                  size="large" 
                  block
                  onClick={() => setIsNewUser(true)}
                >
                  我是新用户，注册后加入
                </Button>
              </Space>
            </div>
          ) : isNewUser ? (
            <NewUserForm />
          ) : (
            <ExistingUserJoin />
          )}
          
          {isNewUser !== null && (
            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Button type="link" onClick={() => setIsNewUser(null)}>
                返回选择
              </Button>
            </div>
          )}
        </Card>

        <div className={styles.footer}>
          <Text type="secondary">© 2025 TeamAuth. All rights reserved.</Text>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default InvitePage;
