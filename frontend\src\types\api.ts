/**
 * API 相关类型定义
 * 基于后端 DTO 和 Entity 类生成
 */

// ============= 基础类型 =============

/**
 * API 响应基础结构
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
}

/**
 * 分页请求参数
 */
export interface PageRequest {
  current?: number;
  pageSize?: number;
}

/**
 * 分页响应结构
 */
export interface PageResponse<T> {
  list: T[];
  total: number;
  current: number;
  pageSize: number;
}

// ============= 认证相关类型 =============

/**
 * 登录请求（验证码登录）
 */
export interface LoginRequest {
  email: string;
  code: string; // 验证码
}

/**
 * 发送验证码请求
 */
export interface SendVerificationCodeRequest {
  email: string;
  type: 'login' | 'register';
}

/**
 * 发送验证码响应
 */
export interface SendVerificationCodeResponse {
  success: boolean;
  message: string;
  expireMinutes?: number;
  nextSendTime?: number; // 下次可发送时间（秒）
  debugCode?: string; // 验证码（仅开发环境）
}

// 注册功能已移除，统一使用验证码登录/注册流程

/**
 * 用户信息
 */
export interface UserInfo {
  id: number;
  email: string;
  name: string;
}

/**
 * 团队信息（登录响应中的简化版本）
 */
export interface TeamInfo {
  id: number;
  name: string;
  /** @deprecated 使用 role 字段替代 */
  isCreator: boolean;
  role?: TeamRole; // 设为可选，以防后端暂时没有返回
  memberCount: number;
  lastAccessTime: string;
}

/**
 * 登录响应
 */
export interface LoginResponse {
  token: string;
  expiresIn: number;
  user: UserInfo;
  teams: TeamInfo[];
  /** 当前选择的团队信息（用于团队选择响应） */
  team?: TeamInfo;
  /** 团队选择成功标识（用于团队选择响应） */
  teamSelectionSuccess?: boolean;
}

// ============= 团队管理相关类型 =============

/**
 * 创建团队请求
 */
export interface CreateTeamRequest {
  name: string;
  description?: string;
}

/**
 * 更新团队请求
 */
export interface UpdateTeamRequest {
  name: string;
  description?: string;
}

/**
 * 邀请成员请求
 */
export interface InviteMembersRequest {
  emails: string[];
  message?: string;
}

/**
 * 团队详情响应
 */
export interface TeamDetailResponse {
  id: number;
  name: string;
  description?: string;
  createdBy: number;
  memberCount: number;
  /** @deprecated 使用 role 字段替代 */
  isCreator: boolean;
  role?: TeamRole; // 设为可选，以防后端暂时没有返回
  lastAccessTime?: string;
  assignedAt?: string; // 用户加入团队的时间
  isActive?: boolean; // 用户在团队中的状态
  createdAt: string;
  updatedAt: string;
  stats?: TeamStatsData; // 可选的统计数据
}

/**
 * 团队成员响应
 */
export interface TeamMemberResponse {
  id: number;
  accountId: number;
  email: string;
  name: string;
  /** @deprecated 使用 role 字段替代 */
  isCreator: boolean;
  role?: TeamRole; // 设为可选，以防后端暂时没有返回
  assignedAt: string;
  lastAccessTime: string;
  isActive: boolean;
}

// ============= 用户管理相关类型 =============

/**
 * 用户资料响应
 */
export interface UserProfileResponse {
  id: number;
  email: string;
  name: string;
  telephone?: string;
  defaultSubscriptionPlanId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * 更新用户资料请求
 */
export interface UpdateUserProfileRequest {
  name?: string;
  telephone?: string;
  currentPassword?: string;
  newPassword?: string;
}

// ============= 订阅管理相关类型 =============

/**
 * 订阅套餐响应
 */
export interface SubscriptionPlanResponse {
  id: number;
  name: string;
  description: string;
  maxSize: number;
  price: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 创建订阅请求
 */
export interface CreateSubscriptionRequest {
  planId: number;
  duration: number;
}

/**
 * 订阅状态枚举
 */
export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED',
}

/**
 * 订阅响应
 */
export interface SubscriptionResponse {
  id: number;
  accountId: number;
  subscriptionPlanId: number;
  planName: string;
  planDescription: string;
  maxSize: number;
  price: number;
  startDate: string;
  endDate: string;
  status: SubscriptionStatus;
  createdAt: string;
  updatedAt: string;
}

/**
 * 邀请状态枚举
 */
export enum InvitationStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED'
}

/**
 * 团队邀请响应
 */
export interface TeamInvitationResponse {
  id: number;
  teamId: number;
  teamName: string;
  inviterId: number;
  inviterName: string;
  inviterEmail: string;
  inviteeEmail: string;
  inviteeId?: number;
  inviteeName?: string;
  status: InvitationStatus;
  invitedAt: string;
  respondedAt?: string;
  expiresAt: string;
  message?: string;
  createdAt: string;
  updatedAt: string;
  isExpired: boolean;
  canBeResponded: boolean;
  canBeCancelled: boolean;
  invitationLink?: string; // 邀请链接
}

/**
 * 响应邀请请求
 */
export interface RespondInvitationRequest {
  accept: boolean;
  message?: string;
}

// ============= 实体类型 =============

/**
 * 账户实体
 */
export interface Account {
  id: number;
  email: string;
  name: string;
  defaultSubscriptionPlanId: number;
  createdAt: string;
  updatedAt: string;
}



/**
 * 团队实体
 */
export interface Team {
  id: number;
  name: string;
  description?: string;
  createdBy: number;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 团队角色枚举
 */
export enum TeamRole {
  TEAM_CREATOR = 'TEAM_CREATOR',
  TEAM_MEMBER = 'TEAM_MEMBER'
}

/**
 * 团队成员实体
 */
export interface TeamMember {
  id: number;
  teamId: number;
  accountId: number;
  /** @deprecated 使用 role 字段替代 */
  isCreator: boolean;
  role: TeamRole;
  assignedAt: string;
  lastAccessTime: string;
  isActive: boolean;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 订阅套餐实体
 */
export interface SubscriptionPlan {
  id: number;
  name: string;
  description: string;
  maxSize: number;
  price: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 账户订阅实体
 */
export interface AccountSubscription {
  id: number;
  accountId: number;
  subscriptionPlanId: number;
  startDate: string;
  endDate: string;
  status: SubscriptionStatus;
  createdAt: string;
  updatedAt: string;
}



// ============= 用户统计相关类型 =============

/**
 * 用户个人统计数据响应
 */
export interface UserPersonalStatsResponse {
  vehicles: number;
  personnel: number;
  warnings: number;
  alerts: number;
}

/**
 * 团队统计数据
 */
export interface TeamStatsData {
  vehicles: number;
  personnel: number;
  expiring: number;
  overdue: number;
}

/**
 * 用户详细信息响应
 */
export interface UserProfileDetailResponse {
  name: string;
  position: string;
  email: string;
  telephone: string;
  registerDate: string;
  lastLoginTime: string;
  lastLoginTeam: string;
  teamCount: number;
  avatar?: string;
}

// ============= TODO相关类型 =============

/**
 * TODO响应
 */
export interface TodoResponse {
  id: number;
  title: string;
  description?: string;
  status: number; // 0-未完成，1-已完成
  priority: number; // 1-低，2-中，3-高
  userId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * 创建TODO请求
 */
export interface CreateTodoRequest {
  title: string;
  description?: string;
  priority: number;
}

/**
 * 更新TODO请求
 */
export interface UpdateTodoRequest {
  title?: string;
  description?: string;
  status?: number;
  priority?: number;
}

/**
 * TODO统计信息响应
 */
export interface TodoStatsResponse {
  highPriorityCount: number;
  mediumPriorityCount: number;
  lowPriorityCount: number;
  totalCount: number;
  completedCount: number;
  completionPercentage: number;
}

// ============= 邀请链接相关类型 =============

/**
 * 通过邀请链接接受邀请请求
 */
export interface AcceptInvitationByLinkRequest {
  name?: string; // 新用户注册时需要
  email?: string; // 新用户注册时需要
  password?: string; // 新用户注册时需要
  message?: string; // 响应消息
}

/**
 * 通过邀请链接接受邀请响应
 */
export interface AcceptInvitationByLinkResponse {
  success: boolean;
  teamId?: number;
  teamName?: string;
  userId?: number;
  isNewUser?: boolean;
  nextAction?: string;
  errorMessage?: string;
}

/**
 * 邀请信息响应
 */
export interface InvitationInfoResponse {
  success: boolean;
  teamId?: number;
  teamName?: string;
  inviterName?: string;
  message?: string;
  invitedAt?: string;
  expiresAt?: string;
  isExpired?: boolean;
  canBeResponded?: boolean;
  errorMessage?: string;
}

/**
 * 发送邀请响应中的邀请信息
 */
export interface InvitationInfo {
  email: string;
  invitationLink: string;
  success: boolean;
  errorMessage?: string;
}

/**
 * 发送邀请响应
 */
export interface SendInvitationResponse {
  totalCount: number;
  successCount: number;
  failureCount: number;
  invitations: InvitationInfo[];
}
