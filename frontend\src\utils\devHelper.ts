/**
 * 开发环境辅助工具
 * 用于在开发环境中提供调试功能
 */

/**
 * 在开发环境中监听验证码
 * 这个函数会在控制台输出验证码相关信息
 */
export const setupDevVerificationCodeListener = () => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  // 监听后端日志中的验证码（通过WebSocket或其他方式）
  // 这里提供一个简单的模拟实现
  console.log('🔧 开发环境：验证码调试模式已启用');
  console.log('📧 验证码将在后端日志中显示，格式如：');
  console.log('   发送验证码: email=<EMAIL>, code=123456, type=login');
  
  // 可以在这里添加更多开发环境的调试功能
  window.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'VERIFICATION_CODE') {
      console.log('🔑 收到验证码:', event.data.code);
    }
  });
};

/**
 * 开发环境验证码提取器
 * 从后端响应或日志中提取验证码
 */
export const extractVerificationCodeFromLog = (logMessage: string): string | null => {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // 匹配后端日志格式：发送验证码: email=xxx, code=123456, type=xxx
  const codeMatch = logMessage.match(/code=(\d{6})/);
  return codeMatch ? codeMatch[1] : null;
};

/**
 * 开发环境快速填充验证码
 * 在开发环境中提供快速填充验证码的功能
 */
export const quickFillVerificationCode = (code: string) => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  // 查找验证码输入框并自动填充
  const codeInput = document.querySelector('input[placeholder*="验证码"]') as HTMLInputElement;
  if (codeInput) {
    codeInput.value = code;
    codeInput.dispatchEvent(new Event('input', { bubbles: true }));
    console.log('🎯 已自动填充验证码:', code);
  }
};

/**
 * 开发环境调试面板
 */
export const showDevDebugPanel = () => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  // 创建一个简单的调试面板
  const panel = document.createElement('div');
  panel.id = 'dev-debug-panel';
  panel.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    z-index: 9999;
    max-width: 300px;
  `;
  
  panel.innerHTML = `
    <div style="font-weight: bold; margin-bottom: 5px;">🔧 开发调试面板</div>
    <div>验证码调试模式已启用</div>
    <div>请查看浏览器控制台获取验证码</div>
    <button onclick="this.parentElement.remove()" style="margin-top: 5px; background: #ff4d4f; color: white; border: none; padding: 2px 5px; border-radius: 3px; cursor: pointer;">关闭</button>
  `;
  
  // 如果已存在面板，先移除
  const existingPanel = document.getElementById('dev-debug-panel');
  if (existingPanel) {
    existingPanel.remove();
  }
  
  document.body.appendChild(panel);
  
  // 5秒后自动隐藏
  setTimeout(() => {
    if (panel.parentElement) {
      panel.remove();
    }
  }, 5000);
};

/**
 * 开发环境全局调试对象
 */
if (process.env.NODE_ENV === 'development') {
  (window as any).devHelper = {
    extractVerificationCodeFromLog,
    quickFillVerificationCode,
    showDevDebugPanel,
    setupDevVerificationCodeListener,
  };
  
  console.log('🔧 开发环境调试工具已加载，使用 window.devHelper 访问');
}
