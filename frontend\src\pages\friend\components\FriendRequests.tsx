/**
 * 好友请求管理组件
 */

import { SendOutlined, UserOutlined } from '@ant-design/icons';
import {
  Avatar,
  Button,
  Card,
  Empty,
  ExclamationCircleOutlined,
  List,
  message,
  Space,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { FriendService } from '@/services';
import type { Account } from '@/types/api';

const { Text, Title } = Typography;

interface FriendRequest {
  id: number;
  accountId: number;
  invitedBy: number;
  invitedAt: string;
  requestedAt: string;
  status: string;
  account?: Account;
  inviter?: Account;
}

interface FriendRequestsProps {
  onRequestHandled: () => void;
}

const FriendRequests: React.FC<FriendRequestsProps> = ({
  onRequestHandled,
}) => {
  const [loading, setLoading] = useState(false);
  const [sentRequests, setSentRequests] = useState<FriendRequest[]>([]);

  useEffect(() => {
    fetchRequests();
  }, []);

  const fetchRequests = async () => {
    try {
      setLoading(true);
      const sent = await FriendService.getSentFriendRequests();
      setSentRequests(sent);
    } catch (error) {
      console.error('获取好友请求失败:', error);
      message.error('获取好友请求失败');
    } finally {
      setLoading(false);
    }
  };

  // 由于移除了接受、拒绝、取消好友请求的功能，这里只保留查看发送的请求

  if (sentRequests.length === 0 && !loading) {
    return (
      <Card>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无发送的好友请求"
          style={{ padding: '50px 0' }}
        />
      </Card>
    );
  }

  return (
    <Card
      title="发送的好友请求"
      extra={
        <Button onClick={fetchRequests} loading={loading}>
          刷新
        </Button>
      }
    >
      <List
        loading={loading}
        dataSource={sentRequests}
        renderItem={(request) => (
          <List.Item>
            <List.Item.Meta
              avatar={
                <Avatar
                  size={48}
                  icon={<UserOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                >
                  {request.account?.name?.charAt(0).toUpperCase()}
                </Avatar>
              }
              title={
                <Space>
                  <Text strong>{request.account?.name || '未知用户'}</Text>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    (待处理)
                  </Text>
                </Space>
              }
              description={
                <Space direction="vertical" size={4}>
                  <Text type="secondary">{request.account?.email}</Text>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    发送时间: {new Date(request.requestedAt).toLocaleString()}
                  </Text>
                </Space>
              }
            />
          </List.Item>
        )}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 个请求`,
        }}
      />
    </Card>
  );
};

export default FriendRequests;
