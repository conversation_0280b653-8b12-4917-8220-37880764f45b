/**
 * 团队成员管理组件
 * 
 * 功能特性：
 * - 查看团队成员列表及详细信息
 * - 添加新成员（通过邮箱邀请）
 * - 移除团队现有成员
 * - 批量操作支持
 * - 成员搜索和筛选
 * 
 * 权限控制：
 * - 只有团队创建者可以进行成员管理操作
 * - 创建者不能移除自己
 * - 提供详细的操作确认
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Form,
  message,
  Tag,
  Avatar,
  Typography,
  Popconfirm,
  Select,
  Divider,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  UserAddOutlined,
  DeleteOutlined,
  SearchOutlined,
  MailOutlined,
  UserOutlined,
  CrownOutlined,
  TeamOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

// 导入服务和类型
import { TeamService } from '@/services/team';
import { InvitationService } from '@/services/invitation';
import type { TeamDetailResponse, TeamMemberResponse } from '@/types/api';

const { Text, Title } = Typography;
const { TextArea } = Input;

interface TeamMemberManagementProps {
  teamDetail: TeamDetailResponse;
  onRefresh: () => void;
}

const TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({
  teamDetail,
  onRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const [members, setMembers] = useState<TeamMemberResponse[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [inviteModalVisible, setInviteModalVisible] = useState(false);
  const [inviteForm] = Form.useForm();

  useEffect(() => {
    fetchMembers();
  }, []);

  const fetchMembers = async () => {
    try {
      setLoading(true);
      const memberList = await TeamService.getCurrentTeamMembers();
      setMembers(memberList || []);
    } catch (error) {
      console.error('获取团队成员失败:', error);
      message.error('获取团队成员失败');
      setMembers([]); // 确保在错误时设置为空数组
    } finally {
      setLoading(false);
    }
  };

  // 邀请新成员
  const handleInviteMembers = async (values: { emails: string; message?: string }) => {
    try {
      const emailList = values.emails
        .split('\n')
        .map(email => email.trim())
        .filter(email => email);

      // 使用新的邀请链接功能
      const response = await InvitationService.sendInvitations({
        emails: emailList,
        message: values.message
      });

      // 显示详细的发送结果
      if (response.successCount > 0) {
        message.success(`成功发送 ${response.successCount} 个邀请`);

        // 显示邀请链接（可选：在开发环境中显示）
        if (process.env.NODE_ENV === 'development') {
          console.log('邀请链接:', response.invitations.map(inv => ({
            email: inv.email,
            link: inv.invitationLink
          })));
        }
      }

      if (response.failureCount > 0) {
        message.warning(`${response.failureCount} 个邀请发送失败`);
      }

      setInviteModalVisible(false);
      inviteForm.resetFields();
      fetchMembers();
    } catch (error) {
      console.error('邀请成员失败:', error);
      message.error('邀请成员失败');
    }
  };

  // 移除单个成员
  const handleRemoveMember = async (member: TeamMemberResponse) => {
    try {
      await TeamService.removeMember(member.id);
      message.success(`已移除成员：${member.name}`);
      fetchMembers();
      onRefresh();
    } catch (error) {
      console.error('移除成员失败:', error);
      message.error('移除成员失败');
    }
  };

  // 批量移除成员
  const handleBatchRemove = async () => {
    try {
      const memberIds = selectedRowKeys as number[];
      for (const memberId of memberIds) {
        await TeamService.removeMember(memberId);
      }
      message.success(`已移除 ${memberIds.length} 名成员`);
      setSelectedRowKeys([]);
      fetchMembers();
      onRefresh();
    } catch (error) {
      console.error('批量移除成员失败:', error);
      message.error('批量移除成员失败');
    }
  };

  // 筛选成员
  const filteredMembers = (members || []).filter(member =>
    member.name.toLowerCase().includes(searchText.toLowerCase()) ||
    member.email.toLowerCase().includes(searchText.toLowerCase())
  );

  // 停用/启用成员
  const handleToggleMemberStatus = async (member: TeamMemberResponse, isActive: boolean) => {
    try {
      await TeamService.updateMemberStatus(member.id, isActive);
      message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);
      fetchMembers();
      onRefresh();
    } catch (error) {
      console.error('更新成员状态失败:', error);
      message.error('更新成员状态失败');
    }
  };

  // 表格列配置
  const columns: ColumnsType<TeamMemberResponse> = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record) => (
        <Space>
          <Text strong>{name}</Text>
          {record.isCreator && (
            <Tag icon={<CrownOutlined />} color="gold">创建者</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => (
        <Text type="secondary">{email}</Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'status',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '加入时间',
      dataIndex: 'assignedAt',
      key: 'assignedAt',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '最后访问',
      dataIndex: 'lastAccessTime',
      key: 'lastAccessTime',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => {
        if (record.isCreator) {
          return <Text type="secondary">-</Text>;
        }

        return (
          <Space>
            {record.isActive ? (
              <Button
                type="text"
                size="small"
                onClick={() => handleToggleMemberStatus(record, false)}
              >
                停用
              </Button>
            ) : (
              <Button
                type="text"
                size="small"
                onClick={() => handleToggleMemberStatus(record, true)}
              >
                启用
              </Button>
            )}
            <Popconfirm
              title="确认移除成员"
              description={`确定要移除成员 ${record.name} 吗？此操作不可恢复。`}
              onConfirm={() => handleRemoveMember(record)}
              okText="确认"
              cancelText="取消"
              okType="danger"
            >
              <Button
                type="text"
                danger
                size="small"
                icon={<DeleteOutlined />}
              >
                移除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record: TeamMemberResponse) => ({
      disabled: record.isCreator, // 创建者不能被选择
    }),
  };

  return (
    <div>
      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="团队成员总数"
              value={(members || []).length}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃成员"
              value={(members || []).filter(m => m.isActive).length}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="待激活成员"
              value={(members || []).filter(m => !m.isActive).length}
              prefix={<MailOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="管理员"
              value={(members || []).filter(m => m.isCreator).length}
              prefix={<CrownOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Input
                placeholder="搜索成员姓名或邮箱"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 300 }}
                allowClear
              />
              {selectedRowKeys.length > 0 && (
                <Popconfirm
                  title="批量移除成员"
                  description={`确定要移除选中的 ${selectedRowKeys.length} 名成员吗？此操作不可恢复。`}
                  onConfirm={handleBatchRemove}
                  okText="确认"
                  cancelText="取消"
                  okType="danger"
                >
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                  >
                    批量移除 ({selectedRowKeys.length})
                  </Button>
                </Popconfirm>
              )}
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<UserAddOutlined />}
              onClick={() => setInviteModalVisible(true)}
            >
              邀请成员
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 成员列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredMembers}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 名成员`,
            pageSize: 10,
          }}
        />
      </Card>

      {/* 邀请成员弹窗 */}
      <Modal
        title="邀请新成员"
        open={inviteModalVisible}
        onCancel={() => {
          setInviteModalVisible(false);
          inviteForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={inviteForm}
          layout="vertical"
          onFinish={handleInviteMembers}
        >
          <Form.Item
            name="emails"
            label="邮箱地址"
            rules={[
              { required: true, message: '请输入邮箱地址' },
            ]}
            extra="每行一个邮箱地址，支持批量邀请"
          >
            <TextArea
              rows={6}
              placeholder="请输入邮箱地址，每行一个&#10;例如：&#10;<EMAIL>&#10;<EMAIL>"
            />
          </Form.Item>
          <Form.Item
            name="message"
            label="邀请消息（可选）"
            extra="您可以添加一些邀请消息，让被邀请人更好地了解邀请意图"
          >
            <TextArea
              rows={3}
              placeholder="欢迎加入我们的团队！我们期待与您一起工作..."
              maxLength={500}
              showCount
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<MailOutlined />}>
                发送邀请
              </Button>
              <Button onClick={() => setInviteModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TeamMemberManagement;
