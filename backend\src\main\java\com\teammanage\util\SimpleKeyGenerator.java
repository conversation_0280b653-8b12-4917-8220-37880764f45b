package com.teammanage.util;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 简单的密钥生成工具
 * 不依赖Spring框架，可以独立运行
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SimpleKeyGenerator {

    public static void main(String[] args) {
        try {
            System.out.println("=== 生成安全密钥 ===");
            
            // 生成AES-256密钥
            KeyGenerator aesKeyGen = KeyGenerator.getInstance("AES");
            aesKeyGen.init(256);
            SecretKey aesKey = aesKeyGen.generateKey();
            String aesKeyBase64 = Base64.getEncoder().encodeToString(aesKey.getEncoded());
            
            System.out.println("AES-256 加密密钥:");
            System.out.println(aesKeyBase64);
            System.out.println();
            
            // 生成HMAC-SHA256密钥
            KeyGenerator hmacKeyGen = KeyGenerator.getInstance("HmacSHA256");
            hmacKeyGen.init(256);
            SecretKey hmacKey = hmacKeyGen.generateKey();
            String hmacKeyBase64 = Base64.getEncoder().encodeToString(hmacKey.getEncoded());
            
            System.out.println("HMAC-SHA256 签名密钥:");
            System.out.println(hmacKeyBase64);
            System.out.println();
            
            System.out.println("=== 配置示例 ===");
            System.out.println("在 application.yml 中配置:");
            System.out.println("app:");
            System.out.println("  security:");
            System.out.println("    token:");
            System.out.println("      encryption-key: " + aesKeyBase64);
            System.out.println("      hmac-key: " + hmacKeyBase64);
            System.out.println();
            
            System.out.println("=== 环境变量配置 ===");
            System.out.println("生产环境建议使用环境变量:");
            System.out.println("TOKEN_ENCRYPTION_KEY=" + aesKeyBase64);
            System.out.println("TOKEN_HMAC_KEY=" + hmacKeyBase64);
            
        } catch (NoSuchAlgorithmException e) {
            System.err.println("生成密钥失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
