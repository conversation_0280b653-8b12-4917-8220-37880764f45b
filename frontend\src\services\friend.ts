/**
 * 好友管理相关 API 服务
 */

import type {
  Account,
  FriendWithRemark,
  SetFriendRemarkRequest,
} from '@/types/api';
import { apiRequest } from '@/utils/request';

/**
 * 添加好友请求
 */
export interface AddFriendRequest {
  email: string;
}

/**
 * 从好友列表邀请成员请求
 */
export interface InviteFriendsRequest {
  friendIds: number[];
}

/**
 * 好友服务类
 *
 * 提供好友关系管理的所有API接口，包括：
 * - 发送好友请求
 * - 删除好友
 * - 获取好友列表（包含备注信息）
 * - 设置好友备注
 * - 搜索用户
 * - 邀请好友加入团队
 */
export class FriendService {
  /**
   * 删除好友
   *
   * @param friendId 要删除的好友ID
   * @returns Promise<void> 删除成功时resolve
   * @throws 当好友关系不存在或其他业务错误时抛出异常
   */
  static async removeFriend(friendId: number): Promise<void> {
    await apiRequest.delete<string>(`/friends/remove?friendId=${friendId}`);
  }

  /**
   * 获取好友列表（包含备注信息）
   *
   * @returns Promise<FriendWithRemark[]> 当前用户的所有好友信息，包含备注
   * @throws 当网络错误或服务器错误时抛出异常
   */
  static async getFriends(): Promise<FriendWithRemark[]> {
    const response = await apiRequest.get<FriendWithRemark[]>('/friends/list');
    return response.data;
  }

  /**
   * 搜索用户（用于添加好友）
   */
  static async searchUsers(email: string): Promise<Account[]> {
    const response = await apiRequest.get<Account[]>(
      `/users/search?email=${encodeURIComponent(email)}`,
    );
    return response.data;
  }

  /**
   * 获取发送的好友请求列表
   */
  static async getSentFriendRequests(): Promise<any[]> {
    const response = await apiRequest.get<any[]>('/friends/requests/sent');
    return response.data;
  }

  /**
   * 发送好友请求
   */
  static async sendFriendRequest(data: AddFriendRequest): Promise<void> {
    await apiRequest.post<string>('/friends/request', data);
  }

  /**
   * 设置好友备注
   */
  static async setFriendRemark(data: SetFriendRemarkRequest): Promise<void> {
    await apiRequest.post<string>('/friends/remark', data);
  }

  /**
   * 从好友列表邀请成员到团队
   */
  static async inviteFriendsToTeam(data: InviteFriendsRequest): Promise<void> {
    await apiRequest.post<string>(
      '/teams/current/members/invite-friends',
      data,
    );
  }
}

// 导出默认实例
export default FriendService;
