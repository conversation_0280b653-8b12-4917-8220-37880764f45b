/**
 * 订阅管理内容组件
 */

import {
  CrownOutlined,
  HistoryOutlined,
  ReloadOutlined,
  StopOutlined,
  UpOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Button,
  Descriptions,
  Empty,
  Modal,
  message,
  Progress,
  Space,
  Table,
  Tag,
  Typography,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';
import { SubscriptionService } from '@/services';
import type { SubscriptionResponse } from '@/types/api';
import { SubscriptionStatus } from '@/types/api';

const { Title, Text } = Typography;

interface SubscriptionManageContentProps {
  currentSubscription: SubscriptionResponse | null;
  loading: boolean;
  onRefresh: () => void;
}

const SubscriptionManageContent: React.FC<SubscriptionManageContentProps> = ({
  currentSubscription,
  loading,
  onRefresh,
}) => {
  const [subscriptionHistory, setSubscriptionHistory] = useState<
    SubscriptionResponse[]
  >([]);
  const [usageInfo, setUsageInfo] = useState<any>(null);
  const [historyModalVisible, setHistoryModalVisible] = useState(false);

  useEffect(() => {
    if (currentSubscription) {
      fetchSubscriptionHistory();
      fetchUsageInfo();
    }
  }, [currentSubscription]);

  const fetchSubscriptionHistory = async () => {
    try {
      const history = await SubscriptionService.getSubscriptionHistory();
      setSubscriptionHistory(history);
    } catch (error) {
      console.error('获取订阅历史失败:', error);
    }
  };

  const fetchUsageInfo = async () => {
    try {
      const usage = await SubscriptionService.getUsageInfo();
      setUsageInfo(usage);
    } catch (error) {
      console.error('获取使用情况失败:', error);
    }
  };

  const handleRenew = async () => {
    if (!currentSubscription) return;

    try {
      await SubscriptionService.renewSubscription(currentSubscription.id);
      message.success('续费成功！');
      onRefresh();
    } catch (error) {
      console.error('续费失败:', error);
    }
  };

  const handleCancel = async () => {
    if (!currentSubscription) return;

    Modal.confirm({
      title: '确认取消订阅',
      content: '取消订阅后，您将失去当前套餐的所有权益。确定要取消吗？',
      okText: '确认取消',
      cancelText: '我再想想',
      okType: 'danger',
      onOk: async () => {
        try {
          await SubscriptionService.cancelSubscription(currentSubscription.id);
          message.success('订阅已取消');
          onRefresh();
        } catch (error) {
          console.error('取消订阅失败:', error);
        }
      },
    });
  };

  const getStatusTag = (status: SubscriptionStatus) => {
    const statusMap = {
      [SubscriptionStatus.ACTIVE]: { color: 'green', text: '有效' },
      [SubscriptionStatus.EXPIRED]: { color: 'red', text: '已过期' },
      [SubscriptionStatus.CANCELLED]: { color: 'default', text: '已取消' },
      [SubscriptionStatus.PENDING]: { color: 'orange', text: '待激活' },
    };

    const config = statusMap[status] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const historyColumns: ColumnsType<SubscriptionResponse> = [
    {
      title: '套餐名称',
      dataIndex: ['plan', 'name'],
      key: 'planName',
    },
    {
      title: '价格',
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      render: (price: number) => `¥${price}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: SubscriptionStatus) => getStatusTag(status),
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      key: 'endDate',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
  ];

  if (loading) {
    return <div>加载中...</div>;
  }

  if (!currentSubscription) {
    return (
      <Empty
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        description="您还没有任何订阅"
      >
        <Text type="secondary">请前往"套餐选择"页面选择适合您的订阅套餐</Text>
      </Empty>
    );
  }

  return (
    <div>
      {/* 当前订阅信息 */}
      <div style={{ marginBottom: 24 }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 16,
          }}
        >
          <Title level={4} style={{ margin: 0 }}>
            <CrownOutlined /> 当前订阅
          </Title>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={onRefresh}>
              刷新
            </Button>
            <Button
              icon={<HistoryOutlined />}
              onClick={() => setHistoryModalVisible(true)}
            >
              订阅历史
            </Button>
          </Space>
        </div>

        <Descriptions column={2} bordered>
          <Descriptions.Item label="套餐名称">
            {currentSubscription.plan.name}
          </Descriptions.Item>
          <Descriptions.Item label="订阅状态">
            {getStatusTag(currentSubscription.status)}
          </Descriptions.Item>
          <Descriptions.Item label="开始时间">
            {new Date(currentSubscription.startDate).toLocaleDateString()}
          </Descriptions.Item>
          <Descriptions.Item label="结束时间">
            {new Date(currentSubscription.endDate).toLocaleDateString()}
          </Descriptions.Item>
          <Descriptions.Item label="总价格">
            ¥{currentSubscription.totalPrice}
          </Descriptions.Item>
          <Descriptions.Item label="存储上限">
            {currentSubscription.plan.maxSize}GB
          </Descriptions.Item>
        </Descriptions>

        {/* 操作按钮 */}
        <div style={{ marginTop: 16 }}>
          <Space>
            {currentSubscription.status === SubscriptionStatus.ACTIVE && (
              <>
                <Button
                  type="primary"
                  icon={<UpOutlined />}
                  onClick={handleRenew}
                >
                  续费
                </Button>
                <Button danger icon={<StopOutlined />} onClick={handleCancel}>
                  取消订阅
                </Button>
              </>
            )}
            {currentSubscription.status === SubscriptionStatus.EXPIRED && (
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={handleRenew}
              >
                重新订阅
              </Button>
            )}
          </Space>
        </div>
      </div>

      {/* 使用情况 */}
      {usageInfo && (
        <div style={{ marginBottom: 24 }}>
          <Title level={4}>使用情况</Title>
          <div style={{ marginBottom: 16 }}>
            <Text>存储使用量</Text>
            <Progress
              percent={Math.round(
                (usageInfo.usedStorage / usageInfo.totalStorage) * 100,
              )}
              format={() =>
                `${usageInfo.usedStorage}GB / ${usageInfo.totalStorage}GB`
              }
              style={{ marginTop: 8 }}
            />
          </div>

          {usageInfo.usedStorage / usageInfo.totalStorage > 0.8 && (
            <Alert
              message="存储空间不足"
              description="您的存储空间使用量已超过80%，建议及时清理或升级套餐。"
              type="warning"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}
        </div>
      )}

      {/* 订阅历史模态框 */}
      <Modal
        title="订阅历史"
        open={historyModalVisible}
        onCancel={() => setHistoryModalVisible(false)}
        footer={null}
        width={800}
      >
        <Table
          columns={historyColumns}
          dataSource={subscriptionHistory}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Modal>
    </div>
  );
};

export default SubscriptionManageContent;
