/**
 * 订阅套餐内容组件
 */

import {
  CheckOutlined,
  CrownOutlined,
  ShoppingCartOutlined,
  StarOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Divider,
  InputNumber,
  List,
  Modal,
  message,
  Row,
  Space,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { SubscriptionService } from '@/services';
import type {
  CreateSubscriptionRequest,
  SubscriptionPlanResponse,
} from '@/types/api';

const { Title, Text } = Typography;

interface SubscriptionPlansContentProps {
  onSubscriptionSuccess?: () => void;
}

const SubscriptionPlansContent: React.FC<SubscriptionPlansContentProps> = ({
  onSubscriptionSuccess,
}) => {
  const [loading, setLoading] = useState(true);
  const [plans, setPlans] = useState<SubscriptionPlanResponse[]>([]);
  const [subscribing, setSubscribing] = useState(false);
  const [selectedPlan, setSelectedPlan] =
    useState<SubscriptionPlanResponse | null>(null);
  const [subscribeModalVisible, setSubscribeModalVisible] = useState(false);
  const [duration, setDuration] = useState(1);

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      const planList = await SubscriptionService.getActivePlans();
      setPlans(planList);
    } catch (error) {
      console.error('获取订阅套餐失败:', error);
      message.error('获取订阅套餐失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = (plan: SubscriptionPlanResponse) => {
    setSelectedPlan(plan);
    setDuration(1);
    setSubscribeModalVisible(true);
  };

  const handleConfirmSubscribe = async () => {
    if (!selectedPlan) return;

    try {
      setSubscribing(true);
      const request: CreateSubscriptionRequest = {
        planId: selectedPlan.id,
        duration,
      };

      await SubscriptionService.createSubscription(request);
      setSubscribeModalVisible(false);
      message.success('订阅成功！');
      onSubscriptionSuccess?.();
    } catch (error) {
      console.error('订阅失败:', error);
    } finally {
      setSubscribing(false);
    }
  };

  const getPlanFeatures = (plan: SubscriptionPlanResponse) => {
    const features = [
      `数据存储上限：${plan.maxSize}GB`,
      '7x24小时技术支持',
      '数据备份与恢复',
      '团队协作功能',
    ];

    if (plan.price > 0) {
      features.push('高级分析报告');
      features.push('API 访问权限');
    }

    if (plan.price >= 100) {
      features.push('专属客户经理');
      features.push('定制化功能');
      features.push('优先技术支持');
    }

    return features;
  };

  const getPlanColor = (plan: SubscriptionPlanResponse) => {
    if (plan.price === 0) return '#52c41a'; // 免费版 - 绿色
    if (plan.price < 100) return '#1890ff'; // 基础版 - 蓝色
    return '#722ed1'; // 专业版 - 紫色
  };

  const getPlanIcon = (plan: SubscriptionPlanResponse) => {
    if (plan.price === 0) return <CheckOutlined />;
    if (plan.price < 100) return <StarOutlined />;
    return <CrownOutlined />;
  };

  const calculatePrice = (plan: SubscriptionPlanResponse, months: number) => {
    return SubscriptionService.calculatePlanPrice(plan, months);
  };

  return (
    <div>
      <Row gutter={[24, 24]}>
        {plans.map((plan) => {
          const features = getPlanFeatures(plan);
          const color = getPlanColor(plan);
          const icon = getPlanIcon(plan);
          const isPopular = plan.price > 0 && plan.price < 100;

          return (
            <Col xs={24} sm={12} lg={8} key={plan.id}>
              <Card
                hoverable
                style={{
                  height: '100%',
                  borderColor: isPopular ? '#1890ff' : undefined,
                  position: 'relative',
                }}
              >
                {isPopular && (
                  <div
                    style={{
                      position: 'absolute',
                      top: -1,
                      right: 24,
                      background: '#1890ff',
                      color: 'white',
                      padding: '4px 12px',
                      borderRadius: '0 0 8px 8px',
                      fontSize: 12,
                      fontWeight: 'bold',
                    }}
                  >
                    推荐
                  </div>
                )}

                <div style={{ textAlign: 'center', marginBottom: 24 }}>
                  <div style={{ fontSize: 48, color, marginBottom: 16 }}>
                    {icon}
                  </div>
                  <Title level={3} style={{ margin: 0, color }}>
                    {plan.name}
                  </Title>
                  <Text type="secondary">{plan.description}</Text>
                </div>

                <div style={{ textAlign: 'center', marginBottom: 24 }}>
                  <div style={{ fontSize: 36, fontWeight: 'bold', color }}>
                    ¥{plan.price}
                    <span style={{ fontSize: 16, fontWeight: 'normal' }}>
                      /月
                    </span>
                  </div>
                  {plan.price === 0 && (
                    <Tag color="green" style={{ marginTop: 8 }}>
                      永久免费
                    </Tag>
                  )}
                </div>

                <List
                  size="small"
                  dataSource={features}
                  renderItem={(feature) => (
                    <List.Item>
                      <CheckOutlined
                        style={{ color: '#52c41a', marginRight: 8 }}
                      />
                      {feature}
                    </List.Item>
                  )}
                  style={{ marginBottom: 24 }}
                />

                <Button
                  type={isPopular ? 'primary' : 'default'}
                  size="large"
                  block
                  icon={<ShoppingCartOutlined />}
                  onClick={() => handleSubscribe(plan)}
                  disabled={plan.price === 0} // 免费套餐不需要订阅
                >
                  {plan.price === 0 ? '当前套餐' : '立即订阅'}
                </Button>
              </Card>
            </Col>
          );
        })}
      </Row>

      {/* 套餐对比 */}
      <Card title="套餐对比" style={{ marginTop: 32 }}>
        <div style={{ overflowX: 'auto' }}>
          {plans.length > 0 && (
            <div>
              {SubscriptionService.comparePlans(plans).map(
                (comparison, index) => (
                  <Row
                    key={index}
                    style={{
                      padding: '12px 0',
                      borderBottom: '1px solid #f0f0f0',
                    }}
                  >
                    <Col span={6}>
                      <Text strong>{comparison.feature}</Text>
                    </Col>
                    {comparison.values.map((value, valueIndex) => (
                      <Col span={6} key={valueIndex}>
                        <Text>
                          {typeof value === 'boolean'
                            ? value
                              ? '✓'
                              : '✗'
                            : value}
                        </Text>
                      </Col>
                    ))}
                  </Row>
                ),
              )}
            </div>
          )}
        </div>
      </Card>

      {/* 订阅确认模态框 */}
      <Modal
        title="确认订阅"
        open={subscribeModalVisible}
        onCancel={() => setSubscribeModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setSubscribeModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="confirm"
            type="primary"
            loading={subscribing}
            onClick={handleConfirmSubscribe}
          >
            确认订阅
          </Button>,
        ]}
      >
        {selectedPlan && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Text strong>套餐：</Text>
              <Text>{selectedPlan.name}</Text>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text strong>订阅时长：</Text>
              <InputNumber
                min={1}
                max={24}
                value={duration}
                onChange={(value) => setDuration(value || 1)}
                addonAfter="个月"
                style={{ marginLeft: 8 }}
              />
            </div>

            <Divider />

            <div>
              <Text strong>价格详情：</Text>
              {(() => {
                const priceInfo = calculatePrice(selectedPlan, duration);
                return (
                  <div style={{ marginTop: 8 }}>
                    <div>原价：¥{priceInfo.originalPrice}</div>
                    {priceInfo.discount > 0 && (
                      <div style={{ color: '#ff4d4f' }}>
                        折扣：-{priceInfo.discount}%
                      </div>
                    )}
                    <div
                      style={{
                        fontSize: 18,
                        fontWeight: 'bold',
                        color: '#1890ff',
                      }}
                    >
                      总计：¥{priceInfo.totalPrice}
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SubscriptionPlansContent;
