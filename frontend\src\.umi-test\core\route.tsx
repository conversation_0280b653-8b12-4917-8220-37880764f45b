// @ts-nocheck
// This file is generated by Um<PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/user","layout":false,"id":"1"},"2":{"name":"login","path":"/user/login","parentId":"1","id":"2"},"3":{"name":"team-select","path":"/user/team-select","parentId":"1","id":"3"},"4":{"path":"/dashboard","name":"仪表盘","icon":"dashboard","parentId":"ant-design-pro-layout","id":"4"},"5":{"path":"/team-management","name":"团队管理","icon":"team","parentId":"ant-design-pro-layout","id":"5"},"6":{"path":"/team","name":"我的团队","icon":"team","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"6"},"7":{"path":"/team","redirect":"/team/list","parentId":"6","id":"7"},"8":{"path":"/team/list","parentId":"6","id":"8"},"9":{"path":"/team/detail","parentId":"6","id":"9"},"10":{"path":"/personal-center","name":"个人中心","icon":"user","layout":false,"id":"10"},"11":{"path":"/user-manage","name":"用户管理","icon":"user","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"11"},"12":{"path":"/subscription","name":"订阅管理","icon":"crown","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"12"},"13":{"path":"/friend","name":"好友管理","icon":"userAdd","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"13"},"14":{"path":"/help","name":"帮助中心","icon":"question","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"14"},"15":{"path":"/","redirect":"/dashboard","parentId":"ant-design-pro-layout","id":"15"},"16":{"path":"*","layout":false,"id":"16"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': require('./EmptyRoute').default,
'2': require('@/pages/user/login/index.tsx').default,
'3': require('@/pages/user/team-select/index.tsx').default,
'4': require('@/pages/Dashboard/index.tsx').default,
'5': require('@/pages/team-management/index.tsx').default,
'6': require('@/pages/team/index.tsx').default,
'7': require('./EmptyRoute').default,
'8': require('@/pages/team/index.tsx').default,
'9': require('@/pages/team/detail/index.tsx').default,
'10': require('@/pages/personal-center/index.tsx').default,
'11': require('@/pages/user/index.tsx').default,
'12': require('@/pages/subscription/index.tsx').default,
'13': require('@/pages/friend/index.tsx').default,
'14': require('@/pages/help/index.tsx').default,
'15': require('./EmptyRoute').default,
'16': require('@/pages/404.tsx').default,
'ant-design-pro-layout': require('H:/projects/IdeaProjects/teamAuth/frontend/src/.umi-test/plugin-layout/Layout.tsx').default,
},
  };
}
