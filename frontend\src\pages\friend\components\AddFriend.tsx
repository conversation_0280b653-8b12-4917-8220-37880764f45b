/**
 * 添加好友组件
 */

import {
  MailOutlined,
  SearchOutlined,
  UserAddOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Button,
  Card,
  Divider,
  Empty,
  Form,
  Input,
  List,
  message,
  Space,
  Typography,
} from 'antd';
import React, { useState } from 'react';
import { FriendService } from '@/services';
import type { Account, AddFriendRequest } from '@/types/api';

const { Text, Paragraph } = Typography;

interface AddFriendProps {
  onAddSuccess: () => void;
}

const AddFriend: React.FC<AddFriendProps> = ({ onAddSuccess }) => {
  const [form] = Form.useForm();
  const [searching, setSearching] = useState(false);
  const [adding, setAdding] = useState<number | null>(null);
  const [searchResults, setSearchResults] = useState<Account[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');

  /**
   * 搜索用户的处理函数
   *
   * 执行流程：
   * 1. 验证输入的邮箱关键词不为空
   * 2. 设置搜索状态，显示加载动画
   * 3. 调用API搜索匹配的用户
   * 4. 更新搜索结果状态
   * 5. 根据结果数量显示相应提示
   * 6. 处理错误情况
   *
   * @param values 表单提交的值，包含邮箱关键词
   */
  const handleSearch = async (values: { email: string }) => {
    const { email } = values;
    if (!email.trim()) {
      message.warning('请输入邮箱关键词');
      return;
    }

    try {
      setSearching(true);
      setSearchKeyword(email);
      const users = await FriendService.searchUsers(email.trim());
      setSearchResults(users);

      if (users.length === 0) {
        message.info('没有找到匹配的用户');
      }
    } catch (error) {
      console.error('搜索用户失败:', error);
      message.error('搜索用户失败，请稍后重试');
    } finally {
      setSearching(false);
    }
  };

  /**
   * 发送好友请求的处理函数
   *
   * 执行流程：
   * 1. 设置添加状态，显示对应用户的加载动画
   * 2. 构造好友请求对象
   * 3. 调用API发送好友请求
   * 4. 从搜索结果中移除已发送请求的用户，避免重复发送
   * 5. 调用父组件成功回调
   * 6. 处理错误情况并显示错误消息
   *
   * @param user 要发送好友请求的用户对象
   */
  const handleAddFriend = async (user: Account) => {
    try {
      setAdding(user.id);
      const request: AddFriendRequest = {
        email: user.email,
      };
      await FriendService.sendFriendRequest(request);

      // 从搜索结果中移除已发送请求的用户，避免重复发送
      setSearchResults((prev) => prev.filter((u) => u.id !== user.id));
      message.success(`已向 "${user.name}" 发送好友请求`);
      onAddSuccess();
    } catch (error) {
      console.error('发送好友请求失败:', error);
      message.error('发送好友请求失败，请稍后重试');
    } finally {
      setAdding(null);
    }
  };

  // 清空搜索结果
  const handleClearSearch = () => {
    setSearchResults([]);
    setSearchKeyword('');
    form.resetFields();
  };

  return (
    <div>
      {/* 搜索表单 */}
      <Card title="搜索用户" style={{ marginBottom: 24 }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          style={{ width: '100%' }}
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: '请输入邮箱关键词' },
              { type: 'email', message: '请输入有效的邮箱格式' },
            ]}
            style={{ flex: 1, marginRight: 16 }}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="输入邮箱地址搜索用户"
              size="large"
              allowClear
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SearchOutlined />}
                loading={searching}
                size="large"
              >
                搜索
              </Button>
              {searchResults.length > 0 && (
                <Button onClick={handleClearSearch} size="large">
                  清空
                </Button>
              )}
            </Space>
          </Form.Item>
        </Form>

        <Divider />

        <Paragraph type="secondary">
          <Text strong>使用说明：</Text>
          <br />• 输入完整的邮箱地址或邮箱关键词进行搜索
          <br />• 找到目标用户后点击"添加好友"按钮
          <br />• 添加成功后可以在"我的好友"中查看
        </Paragraph>
      </Card>

      {/* 搜索结果 */}
      {searchKeyword && (
        <Card
          title={`搜索结果 - "${searchKeyword}"`}
          extra={
            <Text type="secondary">找到 {searchResults.length} 个用户</Text>
          }
        >
          {searchResults.length === 0 ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="没有找到匹配的用户"
              style={{ padding: '50px 0' }}
            >
              <Text type="secondary">请尝试使用其他关键词搜索</Text>
            </Empty>
          ) : (
            <List
              dataSource={searchResults}
              renderItem={(user) => (
                <List.Item
                  actions={[
                    <Button
                      key="add"
                      type="primary"
                      icon={<UserAddOutlined />}
                      loading={adding === user.id}
                      onClick={() => handleAddFriend(user)}
                    >
                      添加好友
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        size={48}
                        icon={<UserOutlined />}
                        style={{ backgroundColor: '#52c41a' }}
                      >
                        {user.name.charAt(0).toUpperCase()}
                      </Avatar>
                    }
                    title={
                      <Space>
                        <Text strong>{user.name}</Text>
                      </Space>
                    }
                    description={
                      <Space direction="vertical" size={4}>
                        <Text type="secondary">{user.email}</Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          注册时间:{' '}
                          {new Date(user.createdAt).toLocaleDateString()}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </Card>
      )}
    </div>
  );
};

export default AddFriend;
