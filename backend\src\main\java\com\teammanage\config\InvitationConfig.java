package com.teammanage.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 邀请配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "app.invitation")
public class InvitationConfig {

    /**
     * 邀请过期时间（小时）
     */
    private int expireHours = 72;

    /**
     * 单次邀请最大数量
     */
    private int maxBatchSize = 10;

    /**
     * 邀请消息最大长度
     */
    private int maxMessageLength = 500;

    /**
     * 是否允许重复邀请同一邮箱
     */
    private boolean allowDuplicateInvitation = false;

    /**
     * 前端基础URL（用于生成邀请链接）
     */
    private String baseUrl = "http://localhost:8002";

    // Getter and Setter methods
    public int getExpireHours() { return expireHours; }
    public void setExpireHours(int expireHours) { this.expireHours = expireHours; }

    public int getMaxBatchSize() { return maxBatchSize; }
    public void setMaxBatchSize(int maxBatchSize) { this.maxBatchSize = maxBatchSize; }

    public int getMaxMessageLength() { return maxMessageLength; }
    public void setMaxMessageLength(int maxMessageLength) { this.maxMessageLength = maxMessageLength; }

    public boolean isAllowDuplicateInvitation() { return allowDuplicateInvitation; }
    public void setAllowDuplicateInvitation(boolean allowDuplicateInvitation) { this.allowDuplicateInvitation = allowDuplicateInvitation; }

    public String getBaseUrl() { return baseUrl; }
    public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }
}
