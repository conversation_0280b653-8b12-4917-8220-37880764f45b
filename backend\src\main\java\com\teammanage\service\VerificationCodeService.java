package com.teammanage.service;

import java.security.SecureRandom;
import java.time.Duration;
import java.time.LocalDateTime;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.github.benmanes.caffeine.cache.Cache;
import com.teammanage.config.VerificationConfig;
import com.teammanage.dto.request.SendVerificationCodeRequest;
import com.teammanage.dto.response.SendVerificationCodeResponse;
import com.teammanage.exception.BusinessException;

/**
 * 验证码服务类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class VerificationCodeService {

    private static final Logger log = LoggerFactory.getLogger(VerificationCodeService.class);

    @Autowired
    private VerificationConfig verificationConfig;

    @Autowired
    private Cache<String, Object> dataCache;

    @Autowired
    private Environment environment;

    /**
     * 验证码信息
     */
    public static class VerificationCodeInfo {
        private String code;
        private LocalDateTime createTime;
        private LocalDateTime expireTime;
        private int attempts;
        private String type;

        // Getter and Setter methods
        public String getCode() { return code; }
        public void setCode(String code) { this.code = code; }

        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }

        public LocalDateTime getExpireTime() { return expireTime; }
        public void setExpireTime(LocalDateTime expireTime) { this.expireTime = expireTime; }

        public int getAttempts() { return attempts; }
        public void setAttempts(int attempts) { this.attempts = attempts; }

        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }

    /**
     * 发送验证码
     *
     * @param request 发送验证码请求
     * @return 发送结果
     */
    public SendVerificationCodeResponse sendVerificationCode(SendVerificationCodeRequest request) {
        // 输入验证
        if (request == null) {
            throw new BusinessException("请求不能为空");
        }

        String email = request.getEmail();
        String type = request.getType();

        // 验证邮箱格式
        if (email == null || email.trim().isEmpty()) {
            throw new BusinessException("邮箱不能为空");
        }

        // 验证类型
        if (type == null || (!type.equals("login") && !type.equals("register"))) {
            throw new BusinessException("验证码类型无效");
        }

        String cacheKey = generateCacheKey(email, type);
        
        SendVerificationCodeResponse response = new SendVerificationCodeResponse();
        
        // 检查是否在重发间隔内
        VerificationCodeInfo existingInfo = (VerificationCodeInfo) dataCache.getIfPresent(cacheKey);
        if (existingInfo != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime nextSendTime = existingInfo.getCreateTime().plusSeconds(verificationConfig.getResendInterval());
            
            if (now.isBefore(nextSendTime)) {
                response.setSuccess(false);
                response.setMessage("验证码发送过于频繁，请稍后再试");
                response.setNextSendTime((int) Duration.between(now, nextSendTime).getSeconds());
                return response;
            }
        }
        
        // 生成验证码
        String code = generateVerificationCode();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expireTime = now.plusMinutes(verificationConfig.getExpireMinutes());
        
        // 创建验证码信息
        VerificationCodeInfo codeInfo = new VerificationCodeInfo();
        codeInfo.setCode(code);
        codeInfo.setCreateTime(now);
        codeInfo.setExpireTime(expireTime);
        codeInfo.setAttempts(0);
        codeInfo.setType(type);
        
        // 存储到缓存
        dataCache.put(cacheKey, codeInfo);
        
        // 这里应该发送邮件，暂时只记录日志
        log.info("发送验证码: email={}, code={}, type={}", email, code, type);
        
        response.setSuccess(true);
        response.setMessage("验证码发送成功");
        response.setNextSendTime(verificationConfig.getResendInterval());
        response.setExpireMinutes(verificationConfig.getExpireMinutes());

        // 开发环境下返回验证码用于调试
        if (isDevelopmentEnvironment()) {
            response.setDebugCode(code);
        }

        return response;
    }

    /**
     * 验证验证码（线程安全）
     *
     * @param email 邮箱
     * @param code 验证码
     * @param type 类型
     * @return 是否验证成功
     */
    public synchronized boolean verifyCode(String email, String code, String type) {
        String cacheKey = generateCacheKey(email, type);
        VerificationCodeInfo codeInfo = (VerificationCodeInfo) dataCache.getIfPresent(cacheKey);

        if (codeInfo == null) {
            throw new BusinessException("验证码不存在或已过期");
        }

        // 检查是否过期
        if (LocalDateTime.now().isAfter(codeInfo.getExpireTime())) {
            dataCache.invalidate(cacheKey);
            throw new BusinessException("验证码已过期");
        }

        // 检查尝试次数
        if (codeInfo.getAttempts() >= verificationConfig.getMaxAttempts()) {
            dataCache.invalidate(cacheKey);
            throw new BusinessException("验证码尝试次数过多，请重新获取");
        }

        // 验证验证码
        if (!code.equals(codeInfo.getCode())) {
            // 增加尝试次数
            codeInfo.setAttempts(codeInfo.getAttempts() + 1);
            dataCache.put(cacheKey, codeInfo);
            throw new BusinessException("验证码错误");
        }

        // 验证成功，删除验证码
        dataCache.invalidate(cacheKey);
        log.info("验证码验证成功: email={}, type={}", email, type);

        return true;
    }

    /**
     * 生成验证码
     * 
     * @return 验证码
     */
    private String generateVerificationCode() {
        SecureRandom random = new SecureRandom();
        StringBuilder code = new StringBuilder();

        for (int i = 0; i < verificationConfig.getCodeLength(); i++) {
            code.append(random.nextInt(10));
        }

        return code.toString();
    }

    /**
     * 生成缓存键
     *
     * @param email 邮箱
     * @param type 类型
     * @return 缓存键
     */
    private String generateCacheKey(String email, String type) {
        return "verification_code:" + type + ":" + email;
    }

    /**
     * 检查是否为开发环境
     *
     * @return 是否为开发环境
     */
    private boolean isDevelopmentEnvironment() {
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("dev".equals(profile) || "development".equals(profile)) {
                return true;
            }
        }
        // 如果没有激活的profile，检查默认profile
        String[] defaultProfiles = environment.getDefaultProfiles();
        for (String profile : defaultProfiles) {
            if ("dev".equals(profile) || "development".equals(profile)) {
                return true;
            }
        }
        // 默认情况下，如果没有设置profile，认为是开发环境
        return activeProfiles.length == 0 && defaultProfiles.length == 1 && "default".equals(defaultProfiles[0]);
    }
}
