((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__invite__token'],
{ "src/pages/invite/[token].tsx": function (module, exports, __mako_require__){
/**
 * 邀请链接处理页面
 * 路由: /invite/:token
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _components = __mako_require__("src/components/index.ts");
var _services = __mako_require__("src/services/index.ts");
var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
    return {
        container: {
            display: 'flex',
            flexDirection: 'column',
            height: '100vh',
            overflow: 'auto',
            backgroundImage: "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
            backgroundSize: '100% 100%'
        },
        content: {
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '32px 16px'
        },
        header: {
            marginBottom: 40,
            textAlign: 'center'
        },
        logo: {
            marginBottom: 16
        },
        title: {
            marginBottom: 0
        },
        inviteCard: {
            width: '100%',
            maxWidth: 500,
            boxShadow: token.boxShadowTertiary
        },
        footer: {
            marginTop: 40,
            textAlign: 'center'
        }
    };
});
const InvitePage = ()=>{
    _s();
    const { token } = (0, _max.useParams)();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [processing, setProcessing] = (0, _react.useState)(false);
    const [result, setResult] = (0, _react.useState)(null);
    const [isNewUser, setIsNewUser] = (0, _react.useState)(null);
    const { styles } = useStyles();
    // 自定义邮箱验证函数
    const validateEmail = (email)=>{
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };
    // 处理邀请接受（现有用户）
    const handleAcceptAsExistingUser = async ()=>{
        if (!token) return;
        setProcessing(true);
        try {
            const response = await _services.InvitationService.acceptInvitationByLink(token, {});
            if (response.success) setResult({
                type: 'success',
                title: '加入成功！',
                message: response.nextAction || '您已成功加入团队',
                teamName: response.teamName,
                isNewUser: response.isNewUser
            });
            else setResult({
                type: 'error',
                title: '加入失败',
                message: response.errorMessage || '处理邀请时发生错误'
            });
        } catch (error) {
            console.error('处理邀请失败:', error);
            setResult({
                type: 'error',
                title: '加入失败',
                message: '网络错误，请稍后重试'
            });
        } finally{
            setProcessing(false);
        }
    };
    // 处理新用户注册并加入
    const handleRegisterAndJoin = async (values)=>{
        if (!token) return;
        setProcessing(true);
        try {
            const response = await _services.InvitationService.acceptInvitationByLink(token, values);
            if (response.success) setResult({
                type: 'success',
                title: '注册并加入成功！',
                message: response.nextAction || '您已成功注册并加入团队',
                teamName: response.teamName,
                isNewUser: response.isNewUser
            });
            else setResult({
                type: 'error',
                title: '注册失败',
                message: response.errorMessage || '注册时发生错误'
            });
        } catch (error) {
            console.error('注册失败:', error);
            setResult({
                type: 'error',
                title: '注册失败',
                message: '网络错误，请稍后重试'
            });
        } finally{
            setProcessing(false);
        }
    };
    // 新用户注册表单
    const NewUserForm = ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
            name: "newUserJoin",
            size: "large",
            onFinish: handleRegisterAndJoin,
            autoComplete: "off",
            layout: "vertical",
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                    message: "您需要注册一个新账号来加入团队",
                    type: "info",
                    showIcon: true,
                    style: {
                        marginBottom: 24
                    }
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 163,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    label: "姓名",
                    name: "name",
                    rules: [
                        {
                            required: true,
                            message: '请输入您的姓名！'
                        },
                        {
                            max: 100,
                            message: '姓名长度不能超过100字符！'
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 179,
                            columnNumber: 19
                        }, void 0),
                        placeholder: "请输入您的姓名"
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 178,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 170,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    label: "邮箱",
                    name: "email",
                    rules: [
                        {
                            required: true,
                            message: '请输入邮箱！'
                        },
                        {
                            validator: (_, value)=>{
                                if (!value || validateEmail(value)) return Promise.resolve();
                                return Promise.reject(new Error('请输入有效的邮箱地址！'));
                            }
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {}, void 0, false, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 200,
                            columnNumber: 19
                        }, void 0),
                        placeholder: "请输入您的邮箱"
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 199,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 184,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    label: "密码",
                    name: "password",
                    rules: [
                        {
                            required: true,
                            message: '请输入密码！'
                        },
                        {
                            min: 8,
                            message: '密码长度至少8位！'
                        }
                    ],
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Password, {
                        prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LockOutlined, {}, void 0, false, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 214,
                            columnNumber: 19
                        }, void 0),
                        placeholder: "请设置密码（至少8位）"
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 213,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 205,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    label: "留言（可选）",
                    name: "message",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.TextArea, {
                        placeholder: "向团队说点什么...",
                        rows: 3,
                        maxLength: 200
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 223,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 219,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        htmlType: "submit",
                        loading: processing,
                        block: true,
                        size: "large",
                        children: "注册并加入团队"
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 231,
                        columnNumber: 9
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 230,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/invite/[token].tsx",
            lineNumber: 156,
            columnNumber: 5
        }, this);
    // 现有用户加入界面
    const ExistingUserJoin = ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                textAlign: 'center',
                padding: '40px 20px'
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                    style: {
                        fontSize: 64,
                        color: '#1890ff',
                        marginBottom: 24
                    }
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 241,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                    level: 3,
                    children: "欢迎加入团队！"
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 242,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Paragraph, {
                    type: "secondary",
                    children: "检测到您已有账号，点击下方按钮即可加入团队"
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 243,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    size: "large",
                    loading: processing,
                    onClick: handleAcceptAsExistingUser,
                    style: {
                        marginTop: 16
                    },
                    children: "加入团队"
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 246,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/invite/[token].tsx",
            lineNumber: 240,
            columnNumber: 5
        }, this);
    // 结果展示
    const ResultDisplay = ()=>{
        if (!result) return null;
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Result, {
            status: result.type,
            title: result.title,
            subTitle: result.message,
            extra: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    onClick: ()=>_max.history.push('/user/login'),
                    children: "前往登录"
                }, "login", false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 268,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    onClick: ()=>_max.history.push('/'),
                    children: "返回首页"
                }, "home", false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 271,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, false, {
            fileName: "src/pages/invite/[token].tsx",
            lineNumber: 263,
            columnNumber: 7
        }, this);
    };
    // 检查token有效性（这里简化处理，实际可以调用API验证）
    (0, _react.useEffect)(()=>{
        if (!token) setResult({
            type: 'error',
            title: '邀请链接无效',
            message: '邀请链接格式错误或已过期'
        });
    }, [
        token
    ]);
    if (result) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        "团队邀请",
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 294,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 293,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.content,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: styles.inviteCard,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(ResultDisplay, {}, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 301,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 300,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 299,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 304,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/invite/[token].tsx",
        lineNumber: 292,
        columnNumber: 7
    }, this);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        "团队邀请",
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 312,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 311,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.content,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.header,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            direction: "vertical",
                            align: "center",
                            size: "large",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.logo,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("img", {
                                        src: "/logo.svg",
                                        alt: "TeamAuth",
                                        height: 48
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 321,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 320,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.title,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                            level: 2,
                                            children: "团队邀请"
                                        }, void 0, false, {
                                            fileName: "src/pages/invite/[token].tsx",
                                            lineNumber: 324,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "加入团队，开始协作"
                                        }, void 0, false, {
                                            fileName: "src/pages/invite/[token].tsx",
                                            lineNumber: 325,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 323,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 319,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 318,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        className: styles.inviteCard,
                        children: [
                            isNewUser === null ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    textAlign: 'center',
                                    padding: '40px 20px'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 4,
                                        children: "选择加入方式"
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 333,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                        direction: "vertical",
                                        size: "large",
                                        style: {
                                            width: '100%'
                                        },
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "primary",
                                                size: "large",
                                                block: true,
                                                onClick: ()=>setIsNewUser(false),
                                                children: "我已有账号，直接加入"
                                            }, void 0, false, {
                                                fileName: "src/pages/invite/[token].tsx",
                                                lineNumber: 335,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                size: "large",
                                                block: true,
                                                onClick: ()=>setIsNewUser(true),
                                                children: "我是新用户，注册后加入"
                                            }, void 0, false, {
                                                fileName: "src/pages/invite/[token].tsx",
                                                lineNumber: 343,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 334,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 332,
                                columnNumber: 13
                            }, this) : isNewUser ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(NewUserForm, {}, void 0, false, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 353,
                                columnNumber: 13
                            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(ExistingUserJoin, {}, void 0, false, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 355,
                                columnNumber: 13
                            }, this),
                            isNewUser !== null && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    textAlign: 'center',
                                    marginTop: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "link",
                                    onClick: ()=>setIsNewUser(null),
                                    children: "返回选择"
                                }, void 0, false, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 360,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 359,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 330,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.footer,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "© 2025 TeamAuth. All rights reserved."
                        }, void 0, false, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 368,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 367,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 317,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 371,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/invite/[token].tsx",
        lineNumber: 310,
        columnNumber: 5
    }, this);
};
_s(InvitePage, "PoQXHrWSggdtNjG0Hp51KotRzgI=", false, function() {
    return [
        _max.useParams,
        useStyles
    ];
});
_c = InvitePage;
var _default = InvitePage;
var _c;
$RefreshReg$(_c, "InvitePage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__invite__token-async.js.map