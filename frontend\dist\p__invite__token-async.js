((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['p__invite__token'],
{ "src/pages/invite/[token].tsx": function (module, exports, __mako_require__){
/**
 * 邀请链接处理页面
 * 路由: /invite/:token
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _antdstyle = __mako_require__("node_modules/antd-style/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _components = __mako_require__("src/components/index.ts");
var _services = __mako_require__("src/services/index.ts");
var _defaultSettings = /*#__PURE__*/ _interop_require_default._(__mako_require__("config/defaultSettings.ts"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const useStyles = (0, _antdstyle.createStyles)(({ token })=>{
    return {
        container: {
            display: 'flex',
            flexDirection: 'column',
            height: '100vh',
            overflow: 'auto',
            backgroundImage: "url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')",
            backgroundSize: '100% 100%'
        },
        content: {
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '32px 16px'
        },
        header: {
            marginBottom: 40,
            textAlign: 'center'
        },
        logo: {
            marginBottom: 16
        },
        title: {
            marginBottom: 0
        },
        inviteCard: {
            width: '100%',
            maxWidth: 500,
            boxShadow: token.boxShadowTertiary
        },
        footer: {
            marginTop: 40,
            textAlign: 'center'
        }
    };
});
const InvitePage = ()=>{
    _s();
    const { token } = (0, _max.useParams)();
    const [loading, setLoading] = (0, _react.useState)(true);
    const [processing, setProcessing] = (0, _react.useState)(false);
    const [invitationInfo, setInvitationInfo] = (0, _react.useState)(null);
    const [result, setResult] = (0, _react.useState)(null);
    const { styles } = useStyles();
    // 获取邀请信息
    const fetchInvitationInfo = async ()=>{
        if (!token) {
            setResult({
                type: 'error',
                title: '邀请链接无效',
                message: '邀请链接格式错误或已过期'
            });
            setLoading(false);
            return;
        }
        try {
            const response = await _services.InvitationService.getInvitationInfo(token);
            if (response.success) setInvitationInfo(response);
            else setResult({
                type: 'error',
                title: '邀请链接无效',
                message: response.errorMessage || '无法获取邀请信息'
            });
        } catch (error) {
            console.error('获取邀请信息失败:', error);
            setResult({
                type: 'error',
                title: '获取邀请信息失败',
                message: '网络错误，请稍后重试'
            });
        } finally{
            setLoading(false);
        }
    };
    // 处理邀请接受
    const handleAcceptInvitation = async ()=>{
        if (!token) return;
        setProcessing(true);
        try {
            const response = await _services.InvitationService.acceptInvitationByLink(token, {});
            if (response.success) setResult({
                type: 'success',
                title: '加入成功！',
                message: response.nextAction || '您已成功加入团队',
                teamName: response.teamName,
                isNewUser: response.isNewUser
            });
            else setResult({
                type: 'error',
                title: '加入失败',
                message: response.errorMessage || '处理邀请时发生错误'
            });
        } catch (error) {
            console.error('处理邀请失败:', error);
            setResult({
                type: 'error',
                title: '加入失败',
                message: '网络错误，请稍后重试'
            });
        } finally{
            setProcessing(false);
        }
    };
    // 处理取消操作
    const handleCancel = ()=>{
        _max.history.push('/');
    };
    // 邀请确认界面
    const InvitationConfirm = ()=>{
        if (!invitationInfo) return null;
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
            style: {
                textAlign: 'center',
                padding: '40px 20px'
            },
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                    style: {
                        fontSize: 64,
                        color: '#1890ff',
                        marginBottom: 24
                    }
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 158,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                    level: 3,
                    children: "团队邀请"
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 159,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        marginBottom: 32
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        direction: "vertical",
                        size: "middle",
                        style: {
                            width: '100%'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "您被邀请加入团队："
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 164,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 165,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                        level: 4,
                                        style: {
                                            margin: '8px 0',
                                            color: '#1890ff'
                                        },
                                        children: invitationInfo.teamName
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 166,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 163,
                                columnNumber: 13
                            }, this),
                            invitationInfo.inviterName && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "邀请人："
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 173,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        strong: true,
                                        children: invitationInfo.inviterName
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 174,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 172,
                                columnNumber: 15
                            }, this),
                            invitationInfo.message && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        type: "secondary",
                                        children: "邀请消息："
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 180,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("br", {}, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 181,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        italic: true,
                                        children: [
                                            '"',
                                            invitationInfo.message,
                                            '"'
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 182,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 179,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 162,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 161,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Divider, {}, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 188,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        marginTop: 24
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                            level: 4,
                            style: {
                                marginBottom: 24
                            },
                            children: "您确定要加入此团队吗？"
                        }, void 0, false, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 191,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            size: "large",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    size: "large",
                                    loading: processing,
                                    onClick: handleAcceptInvitation,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 201,
                                        columnNumber: 21
                                    }, void 0),
                                    children: "确认加入"
                                }, void 0, false, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 196,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    size: "large",
                                    onClick: handleCancel,
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseCircleOutlined, {}, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 208,
                                        columnNumber: 21
                                    }, void 0),
                                    children: "取消"
                                }, void 0, false, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 205,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 195,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 190,
                    columnNumber: 9
                }, this),
                invitationInfo.isExpired && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                    message: "邀请已过期",
                    description: "此邀请链接已过期，请联系团队管理员重新发送邀请。",
                    type: "warning",
                    showIcon: true,
                    style: {
                        marginTop: 24
                    }
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 216,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "src/pages/invite/[token].tsx",
            lineNumber: 157,
            columnNumber: 7
        }, this);
    };
    // 结果展示
    const ResultDisplay = ()=>{
        if (!result) return null;
        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Result, {
            status: result.type,
            title: result.title,
            subTitle: result.message,
            extra: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    type: "primary",
                    onClick: ()=>_max.history.push('/user/login'),
                    children: "前往登录"
                }, "login", false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 238,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                    onClick: ()=>_max.history.push('/'),
                    children: "返回首页"
                }, "home", false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 241,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, false, {
            fileName: "src/pages/invite/[token].tsx",
            lineNumber: 233,
            columnNumber: 7
        }, this);
    };
    // 页面加载时获取邀请信息
    (0, _react.useEffect)(()=>{
        fetchInvitationInfo();
    }, [
        token
    ]);
    // 加载中状态
    if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        "团队邀请",
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 259,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 258,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.content,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: styles.inviteCard,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '60px 20px'
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                size: "large"
                            }, void 0, false, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 267,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginTop: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    type: "secondary",
                                    children: "正在加载邀请信息..."
                                }, void 0, false, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 269,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/invite/[token].tsx",
                                lineNumber: 268,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 266,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 265,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 264,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 274,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/invite/[token].tsx",
        lineNumber: 257,
        columnNumber: 7
    }, this);
    // 显示结果页面
    if (result) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        "团队邀请",
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 284,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 283,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.content,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: styles.inviteCard,
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(ResultDisplay, {}, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 291,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 290,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 289,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 294,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/invite/[token].tsx",
        lineNumber: 282,
        columnNumber: 7
    }, this);
    // 显示邀请确认页面
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        className: styles.container,
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_max.Helmet, {
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("title", {
                    children: [
                        "团队邀请",
                        _defaultSettings.default.title && ` - ${_defaultSettings.default.title}`
                    ]
                }, void 0, true, {
                    fileName: "src/pages/invite/[token].tsx",
                    lineNumber: 303,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 302,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: styles.content,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.header,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                            direction: "vertical",
                            align: "center",
                            size: "large",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.logo,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("img", {
                                        src: "/logo.svg",
                                        alt: "TeamAuth",
                                        height: 48
                                    }, void 0, false, {
                                        fileName: "src/pages/invite/[token].tsx",
                                        lineNumber: 312,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 311,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: styles.title,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                            level: 2,
                                            children: "团队邀请"
                                        }, void 0, false, {
                                            fileName: "src/pages/invite/[token].tsx",
                                            lineNumber: 315,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            type: "secondary",
                                            children: "加入团队，开始协作"
                                        }, void 0, false, {
                                            fileName: "src/pages/invite/[token].tsx",
                                            lineNumber: 316,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/invite/[token].tsx",
                                    lineNumber: 314,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 310,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 309,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                        className: styles.inviteCard,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(InvitationConfirm, {}, void 0, false, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 322,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 321,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: styles.footer,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "© 2025 TeamAuth. All rights reserved."
                        }, void 0, false, {
                            fileName: "src/pages/invite/[token].tsx",
                            lineNumber: 326,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/invite/[token].tsx",
                        lineNumber: 325,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 308,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                fileName: "src/pages/invite/[token].tsx",
                lineNumber: 329,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/invite/[token].tsx",
        lineNumber: 301,
        columnNumber: 5
    }, this);
};
_s(InvitePage, "BKdBJ5I1qD3mEg2QSXn93lLBB0A=", false, function() {
    return [
        _max.useParams,
        useStyles
    ];
});
_c = InvitePage;
var _default = InvitePage;
var _c;
$RefreshReg$(_c, "InvitePage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=p__invite__token-async.js.map